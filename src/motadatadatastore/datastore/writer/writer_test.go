/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/* Change Logs:
* Date                   Author          		Notes
* 2025-03-05			 <PERSON><PERSON><PERSON>-5190  Migrated constants from datastore package to utils package to match SonarQube Standard
* 2025-05-02             Vedant Dokania         Motadata-6080  For initial 1 lakh records restricted the writing of invalid indexable column
* 2025-04-05			 Dhaval <PERSON>ra			<PERSON>ata-6076  Added Test Case For Read String Column
* 2025-05-05			 Swapnil <PERSON><PERSON> Dave		<PERSON>A-6078 renamed the valuebuffer to readbuffer

 */

package writer

import (
	"bufio"
	bytes2 "bytes"
	"encoding/binary"
	"errors"
	"fmt"
	"github.com/kelindar/bitmap"
	"github.com/stretchr/testify/assert"
	"github.com/valyala/gozstd"
	"math"
	"motadatadatastore/cache"
	. "motadatadatastore/codec"
	"motadatadatastore/datastore"
	. "motadatadatastore/storage"
	"motadatadatastore/utils"
	"os"
	"path/filepath"
	"sort"
	"strconv"
	"strings"
	"sync"
	"sync/atomic"
	"testing"
	"time"
)

var (
	horizontalWriter *HorizontalWriter

	verticalWriter *VerticalWriter

	staticMetricWriter *StaticMetricWriter

	valueBuffers = make([][]byte, utils.MaxValueBufferBytes)

	indexWriters []*IndexWriter
)

// make sure this should be run first in writer_test
func TestInstanceMetricMappings(t *testing.T) {

	utils.CleanUpStores()

	datastore.Init()

	cache.InitCacheEngine()

	for index := range valueBuffers {

		valueBuffers[index] = make([]byte, utils.MaxValueBufferBytes)

	}

	assertions := assert.New(t)

	store := datastore.GetStore("mappingStore", utils.PerformanceMetric, true, true, encoder, mockMetricTokenizer)

	key1 := "1^x^system.process"

	key2 := "1^y^system.process"

	key3 := "1^z^system.process"

	err := verticalWriter.flushStringMetric(key1, "x", "system.cpu.percent", "1")

	assertions.Nil(err)

	err = verticalWriter.flushStringMetric(key2, "y", "system.cpu.percent", "1")

	assertions.Nil(err)

	err = verticalWriter.flushStringMetric(key3, "z", "system.cpu.percent", "1")

	assertions.Nil(err)

	// in mapping store  we will get 3 values  x ->3 , y-> 5 , z -> 7 " string mappings on odd number "

	store = datastore.GetStore("object-mappings", utils.Mapping, false, true, encoder, mockMetricTokenizer)

	found, count, err := store.GetStringMapping("1" + utils.GroupSeparator + "x")

	assertions.Nil(err)

	assertions.True(found)

	assertions.Equal(int32(3), count)

	found, count, err = store.GetStringMapping("1" + utils.GroupSeparator + "y")

	assertions.Nil(err)

	assertions.True(found)

	assertions.Equal(int32(5), count)

	found, count, err = store.GetStringMapping("1" + utils.GroupSeparator + "z")

	assertions.Nil(err)

	assertions.True(found)

	assertions.Equal(int32(7), count)

}

func TestWriteNumericMetricINTToFLOAT(t *testing.T) {

	assertions := assert.New(t)

	expectedValues := []float64{1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1}

	store := datastore.GetStore("22082022-test-write-numeric-metric-int-to-float", utils.PerformanceMetric, true, true, encoder, mockMetricTokenizer)

	key := "1^int.to.float"

	objectId := "1"

	metricKey := "1^int.to.float"

	datastore.AddFloatingColumn("int.to.float")

	writer := NewVerticalWriter(0, nil)

	writer.shutdown = true

	writer.Start()

	expectedTicks := []int32{241076346, 241076351, 241076356, 241076361, 241076366, 241076371, 241076376, 241076381, 241076386, 241076391, 241076396, 241076401, 241076406, 241076411, 241076416, 241076421, 241076426, 241076431, 241076436, 241076441, 241076446, 241076451, 241076456, 241076461, 241076466, 241076471, 241076476, 241076481, 241076486, 241076491, 241076496, 241076501}

	for i := 0; i < 30; i++ { //inserting int value

		writer.tick = time.UnixMilli(utils.SecondsToUnixMillis(expectedTicks[i])).Unix()

		err := writer.writeMetricCacheEntries(key, "int.to.float", "22082022-test-write-numeric-metric-int-to-float", int64(expectedValues[i]), objectId, Invalid, false)

		assertions.Nil(err)
	}

	for i := 30; i < 32; i++ { //inserting float value

		writer.tick = time.UnixMilli(utils.SecondsToUnixMillis(expectedTicks[i])).Unix()

		err := writer.writeMetricCacheEntries(key, "int.to.float", "22082022-test-write-numeric-metric-int-to-float", expectedValues[i], objectId, Invalid, false)

		assertions.Nil(err)
	}

	datastore.Close()

	datastore.Init()

	store = datastore.GetStore("22082022-test-write-numeric-metric-int-to-float", utils.PerformanceMetric, true, true, encoder, mockMetricTokenizer)

	found, valueBytes, err := store.Get([]byte(metricKey+datastore.DefaultKeyPart), writer.readBuffers[0], encoder, writer.event, &writer.waitGroup, mockMetricTokenizer, false)

	assertions.True(found)

	assertions.Nil(err)

	assertions.NotNil(valueBytes)

	poolIndex, _, _, _, _, _, values, _, err := datastore.GetColumns(GetDataType(valueBytes[0]), valueBytes[0], valueBytes[1:], "", "", 0, decoder)

	defer decoder.MemoryPool.ReleaseFLOAT64Pool(poolIndex)

	assertions.Nil(err)

	assertions.NotNil(values)

	assertions.EqualValues(expectedValues, values)

	found, valueBytes, err = store.Get([]byte(metricKey+utils.KeySeparator+"time"+datastore.DefaultKeyPart), writer.readBuffers[1], encoder, writer.event, &writer.waitGroup, mockMetricTokenizer, true)

	poolIndex, ticks, err := datastore.GetTimeTicks(valueBytes, 0, decoder)

	decoder.MemoryPool.ReleaseINT32Pool(poolIndex)

	assertions.NotNil(ticks)

	assertions.EqualValues(expectedTicks, ticks)

	assertions.Nil(err)

}

func TestWriteNumericMetricINTOverflow(t *testing.T) {

	assertions := assert.New(t)

	length := 86401

	expectedValues := make([]int8, length)

	for index := 0; index < length; index++ {

		expectedValues[index] = 1
	}

	store := datastore.GetStore("22082022-test-write-numeric-metric-int-to-float", utils.PerformanceMetric, true, true, encoder, mockMetricTokenizer)

	key := "1^int.to.float.overflow"

	metricKey := "1^int.to.float.overflow"

	objectId := "1"

	expectedTicks := make([]int32, length)

	tick := int32(241076346)

	for index := 0; index < length; index++ {

		expectedTicks[index] = tick + int32(index*5)
	}

	for i := 0; i < 86400; i++ { //inserting int value

		verticalWriter.tick = time.UnixMilli(utils.SecondsToUnixMillis(expectedTicks[i])).Unix()

		err := verticalWriter.writeMetricCacheEntries(key, "int.to.float", "22082022-test-write-numeric-metric-int-to-float", int64(expectedValues[i]), objectId, Invalid, false)

		assertions.Nil(err)
	}

	parts := store.GetMaxPart(utils.GetHash64([]byte(metricKey)))

	assertions.Equal(uint16(8), parts)

	found, valueBytes, err := store.Get([]byte(metricKey+datastore.DefaultKeyPart), verticalWriter.readBuffers[0], encoder, verticalWriter.event, &verticalWriter.waitGroup, mockMetricTokenizer, true)

	assertions.True(found)

	assertions.Nil(err)

	assertions.NotNil(valueBytes)

	poolIndex, _, size, _, _, _, _, _, err := datastore.GetColumns(GetDataType(valueBytes[0]), valueBytes[0], valueBytes[1:], "", "", 0, decoder)

	defer decoder.MemoryPool.ReleaseINT8Pool(poolIndex)

	assertions.Nil(err)

	assertions.NotNil(size)

	assertions.EqualValues(expectedValues[:len(size)], size)

	found, valueBytes, err = store.Get([]byte(metricKey+utils.KeySeparator+"time"+datastore.DefaultKeyPart), verticalWriter.readBuffers[1], encoder, verticalWriter.event, &verticalWriter.waitGroup, mockMetricTokenizer, true)

	tickIndex, ticks, err := datastore.GetTimeTicks(valueBytes, 0, decoder)

	defer decoder.MemoryPool.ReleaseINT32Pool(tickIndex)

	assertions.NotNil(ticks)

	assertions.EqualValues(expectedTicks[:len(ticks)], ticks)

	assertions.Nil(err)

}

func TestWriteNumericMetricFLOATToINT(t *testing.T) {

	assertions := assert.New(t)

	expectedValues := []float64{1, 1}

	datastore.GetStore("22082022-test-write-numeric-metric-float-to-int", utils.PerformanceMetric, true, true, encoder, mockMetricTokenizer)

	key := "1^float.to.int"

	datastore.AddFloatingColumn("float.to.int")

	objectId := "1"

	expectedTicks := []int32{241076346, 241076351}

	storeName := "22082022-test-write-numeric-metric-float-to-int"

	verticalWriter.tick = time.UnixMilli(utils.SecondsToUnixMillis(expectedTicks[0])).Unix()

	err := verticalWriter.writeMetricCacheEntries(key, "float.to.int", storeName, expectedValues[0], objectId, 0, false)

	assertions.Nil(err)

	verticalWriter.tick = time.UnixMilli(utils.SecondsToUnixMillis(expectedTicks[1])).Unix()

	err = verticalWriter.writeMetricCacheEntries(key, "float.to.int", storeName, int64(expectedValues[1]), objectId, 0, false)

	assertions.Nil(err)

	for index, tick := range verticalWriter.verticalCacheEntries[objectId+utils.KeySeparator+storeName][key][0] {

		assertions.Equal(expectedTicks[index], tick.(int32))
	}

	for index, value := range expectedValues {

		assertions.Equal(value, verticalWriter.verticalCacheEntries[objectId+utils.KeySeparator+storeName][key][1][index+1].(float64))
	}

}

func TestGarbageColumn(t *testing.T) {

	assertions := assert.New(t)

	verticalWriter = NewVerticalWriter(1, []*StaticMetricWriter{staticMetricWriter})

	verticalWriter.Start()

	datastore.AddGarbageColumn("system.uptime")

	metrics := []utils.MotadataMap{
		{
			"datatype":  datastore.FloatingColumn,
			"object.id": 1,
			"metric":    "system.uptime",
			"instance":  "instance",
			"value":     float64(15),
		},
	}

	buffer := bytes2.Buffer{}

	blocks := make(map[string][]utils.MotadataMap)

	blocks["1"+utils.GroupSeparator+"instance"] = metrics

	PackPerformanceMetricDataBlockV1(blocks, &buffer)

	atomic.AddInt32(&verticalWriter.Pending, 1)

	verticalWriter.Events <- utils.MotadataMap{
		utils.Plugin:            "test-demo",
		utils.Tick:              time.Now().Unix(),
		datastore.DatastoreType: utils.PerformanceMetric,
		utils.BatchSize:         1,
		utils.Buffer:            buffer.Bytes(),
	}

	time.Sleep(time.Second * 3)

	garbageStore := datastore.GetStore(verticalWriter.plugin, utils.StaticMetric, false, true, verticalWriter.encoder, verticalWriter.tokenizers[1])

	assertions.NotNil(garbageStore)

	keys, err := garbageStore.GetKeys(nil, nil, false, Invalid)

	assertions.Nil(err)

	found := false

	for index := range keys {

		if strings.Contains(string(keys[index]), "1^instance^system.uptime^0") {

			found = true

		}

	}

	assertions.True(found)

}

func TestWriteNumericMetricNonFloatingMetric(t *testing.T) {

	assertions := assert.New(t)

	expectedValues := []int64{1, 1}

	store := datastore.GetStore("22082022-test-write-numeric-metric-non-floating-metric", utils.PerformanceMetric, true, true, encoder, mockMetricTokenizer)

	key := "1^non.floating.metric"

	objectId := "1"

	storeName := store.GetName()

	expectedTicks := []int32{241076346, 241076351}

	verticalWriter.tick = time.UnixMilli(utils.SecondsToUnixMillis(expectedTicks[0])).Unix()

	err := verticalWriter.writeMetricCacheEntries(key, storeName, "22082022-test-write-numeric-metric-non-floating-metric", float64(expectedValues[0]), objectId, 0, false)

	assertions.Nil(err)

	verticalWriter.tick = time.UnixMilli(utils.SecondsToUnixMillis(expectedTicks[1])).Unix()

	err = verticalWriter.writeMetricCacheEntries(key, storeName, "22082022-test-write-numeric-metric-non-floating-metric", float64(expectedValues[1]), objectId, 0, false)

	assertions.Nil(err)

	for index, tick := range verticalWriter.verticalCacheEntries[objectId+utils.KeySeparator+storeName][key][0] {
		assertions.Equal(expectedTicks[index], tick.(int32))
	}

	for index, value := range expectedValues {

		assertions.Equal(value, verticalWriter.verticalCacheEntries[objectId+utils.KeySeparator+storeName][key][1][index+1].(int64))
	}

}

func TestWriteNumericMetricInvalidDataType(t *testing.T) {

	assertions := assert.New(t)

	key := "1^invalid.metric"

	err := verticalWriter.writeMetricCacheEntries(key, "invalid.metric", "22082022-test-write-invalid-numeric-metric", "invalid", "1", Invalid, false)

	assertions.NotNil(err)

}

func TestWriteStatusMetric(t *testing.T) {

	assertions := assert.New(t)

	expectedValues := []int64{10, 10, 10999999, 10, 10, 10905859768, 10, 1077777, 10, 10, 10, 10, 10, 10, 10, 10, 108888, 10, 10, 10, 10, 10, 10, 10, 19990, 10, 10, 10, 10, 10, 10, 10}

	store := datastore.GetStore("22082022-test-write-status-metric", utils.PerformanceMetric, true, true, encoder, mockMetricTokenizer)

	key := "1^status.metric"

	metricKey := "1^status.metric"

	objectId := "1"

	storeName := store.GetName()

	expectedTicks := []int32{241076346, 241076351, 241076356, 241076361, 241076366, 241076371, 241076376, 241076381, 241076386, 241076391, 241076396, 241076401, 241076406, 241076411, 241076416, 241076421, 241076426, 241076431, 241076436, 241076441, 241076446, 241076451, 241076456, 241076461, 241076466, 241076471, 241076476, 241076481, 241076486, 241076491, 241076496, 241076501}

	for i := 0; i < 32; i++ {

		verticalWriter.tick = time.UnixMilli(utils.SecondsToUnixMillis(expectedTicks[i])).Unix()

		err := verticalWriter.writeMetricCacheEntries(key, "status.metric", storeName, expectedValues[i], objectId, GetDataTypeINT(int(expectedValues[i])), false)

		assertions.Nil(err)
	}

	found, valueBytes, err := store.Get([]byte(metricKey+datastore.DefaultKeyPart), verticalWriter.readBuffers[0], encoder, verticalWriter.event, &verticalWriter.waitGroup, mockMetricTokenizer, true)

	assertions.True(found)

	assertions.Nil(err)

	assertions.NotNil(valueBytes)

	poolIndex, _, _, _, _, values, _, _, err := datastore.GetColumns(GetDataType(valueBytes[0]), valueBytes[0], valueBytes[1:], "", "", 0, decoder)

	defer decoder.MemoryPool.ReleaseINT64Pool(poolIndex)

	assertions.Nil(err)

	assertions.NotNil(values)

	assertions.EqualValues(expectedValues, values)

	found, valueBytes, err = store.Get([]byte(metricKey+utils.KeySeparator+"time"+datastore.DefaultKeyPart), verticalWriter.readBuffers[1], encoder, verticalWriter.event, &verticalWriter.waitGroup, mockMetricTokenizer, true)

	tickIndex, ticks, err := datastore.GetTimeTicks(valueBytes, 0, decoder)

	defer decoder.MemoryPool.ReleaseINT32Pool(tickIndex)

	assertions.NotNil(ticks)

	assertions.EqualValues(expectedTicks, ticks)

	assertions.Nil(err)

}

// BugId :-24708
func TestWriteAvailabilityMetric(t *testing.T) {

	assertions := assert.New(t)

	expectedValues := []int8{10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10}

	store := datastore.GetStore("22082022-test-write-status-metric", utils.PerformanceMetric, true, true, encoder, mockMetricTokenizer)

	key := "1^status.metric%v"

	metricKey := "1^status.metric%v"

	objectId := "1"

	storeName := store.GetName()

	expectedTicks := []int32{241076346, 241076351, 241076356, 241076361, 241076366, 241076371, 241076376, 241076381, 241076386, 241076391, 241076396, 241076401, 241076406, 241076411, 241076416, 241076421, 241076426, 241076431, 241076436, 241076441, 241076446, 241076451, 241076456, 241076461, 241076466, 241076471, 241076476, 241076481, 241076486, 241076491, 241076496, 241076501}

	for i := 0; i < 32; i++ {

		verticalWriter.tick = time.UnixMilli(utils.SecondsToUnixMillis(expectedTicks[i])).Unix()

		err := verticalWriter.writeMetricCacheEntries(key, "status.metric%v", storeName, int64(expectedValues[i]), objectId, GetDataTypeINT(int(expectedValues[i])), false)

		assertions.Nil(err)
	}

	found, valueBytes, err := store.Get([]byte(metricKey+datastore.DefaultKeyPart), verticalWriter.readBuffers[0], encoder, verticalWriter.event, &verticalWriter.waitGroup, mockMetricTokenizer, true)

	assertions.True(found)

	assertions.Nil(err)

	assertions.NotNil(valueBytes)

	poolIndex, _, values, _, _, _, _, _, err := datastore.GetColumns(GetDataType(valueBytes[0]), valueBytes[0], valueBytes[1:], "", "", 0, decoder)

	defer decoder.MemoryPool.ReleaseINT8Pool(poolIndex)
	assertions.Nil(err)

	assertions.NotNil(values)

	assertions.EqualValues(expectedValues, values)

	found, valueBytes, err = store.Get([]byte(metricKey+utils.KeySeparator+"time"+datastore.DefaultKeyPart), verticalWriter.readBuffers[1], encoder, verticalWriter.event, &verticalWriter.waitGroup, mockMetricTokenizer, true)

	tickIndex, ticks, err := datastore.GetTimeTicks(valueBytes, 0, decoder)

	defer decoder.MemoryPool.ReleaseINT32Pool(tickIndex)

	assertions.NotNil(ticks)

	assertions.EqualValues(expectedTicks, ticks)

	assertions.Nil(err)

}

// trap test cases
func TestTrapWriter(t *testing.T) {

	for key := range verticalWriter.verticalCacheEntries {

		delete(verticalWriter.verticalCacheEntries, key)
	}

	pluginId := int32(2100)

	tick := int64(1663146799)

	keyBuffers := make([][]byte, 8)

	valueBuffers := make([][]byte, 8)

	assertions := assert.New(t)

	datastore.AddBlobColumn("trap.message")

	for i := 0; i < 8; i++ {

		keyBuffers[i] = make([]byte, utils.MaxValueBufferBytes)

		valueBuffers[i] = make([]byte, utils.MaxValueBufferBytes)

	}

	columns := []string{utils.TrapOID, utils.TrapEnterprise, utils.TrapEnterpriseId, utils.TrapMessage, utils.TrapName, utils.TrapSeverity}

	values := []interface{}{"1.3.6.1.2", "cisco1", "0.0.1", "message1", "trap1", "up"}

	dataTypes := []byte{datastore.StringColumn, datastore.StringColumn, datastore.StringColumn, datastore.StringColumn, datastore.StringColumn, datastore.StringColumn}

	rowBuffer := &bytes2.Buffer{}

	for index := range columns {

		PackRowBuffer(columns[index], ToString(values[index]), dataTypes[index], rowBuffer)
	}

	batch := PackEventBatchV3(INT32ToStringValue(pluginId), tick, "10.20.40.108", utils.TrapFlapHistory, rowBuffer)

	atomic.AddInt32(&verticalWriter.Pending, 1)

	verticalWriter.Events <- batch

	time.Sleep(time.Second * 5)

	values = []interface{}{"1.3.6.1.2", "cisco2", "0.0.2", "message2", "trap2", "up"}

	rowBuffer = &bytes2.Buffer{}

	for index := range columns {

		PackRowBuffer(columns[index], ToString(values[index]), dataTypes[index], rowBuffer)
	}

	batch = PackEventBatchV3(INT32ToStringValue(pluginId), tick, "10.20.40.108", utils.TrapFlapHistory, rowBuffer)

	atomic.AddInt32(&verticalWriter.Pending, 1)

	verticalWriter.Events <- batch

	time.Sleep(time.Second * 5)

	datastore.Close()

	datastore.Init()

	key := "10.20.40.108^1.3.6.1.2^"

	store := datastore.GetStore(datastore.GetStoreName(utils.UnixToSeconds(tick), INT32ToStringValue(pluginId)+".flap", datastore.VerticalStore), utils.None, false, true, encoder, mockMetricTokenizer)

	keyBuffers[0] = []byte(key + utils.TrapEnterprise + datastore.DefaultKeyPart)

	keyBuffers[1] = []byte(key + utils.TrapEnterpriseId + datastore.DefaultKeyPart)

	keyBuffers[2] = []byte(key + utils.TrapMessage + datastore.DefaultKeyPart)

	keyBuffers[3] = []byte(key + utils.TrapName + datastore.DefaultKeyPart)

	keyBuffers[4] = []byte(key + utils.TrapEnterprise + utils.KeySeparator + datastore.Time + datastore.DefaultKeyPart)

	keyBuffers[5] = []byte(key + utils.TrapEnterpriseId + utils.KeySeparator + datastore.Time + datastore.DefaultKeyPart)

	keyBuffers[6] = []byte(key + utils.TrapMessage + utils.KeySeparator + datastore.Time + datastore.DefaultKeyPart)

	keyBuffers[7] = []byte(key + utils.TrapName + utils.KeySeparator + datastore.Time + datastore.DefaultKeyPart)

	valueBytes, errs, err := store.GetMultiples(keyBuffers, valueBuffers, encoder, verticalWriter.events, &verticalWriter.waitGroup, verticalWriter.tokenizers[1], false)

	assertions.Nil(err)

	for index := range errs {

		assertions.Nil(errs[index])
	}

	poolIndex, enterprises, err := verticalWriter.decoder.DecodeStringValues(GetEncoding(valueBytes[0][0]), valueBytes[0][1:], string(keyBuffers[0]), store.GetName(), 0)

	assertions.Nil(err)

	assertions.Equal([]string{"cisco1", "cisco2"}, enterprises)

	verticalWriter.decoder.MemoryPool.ReleaseStringPool(poolIndex)

	poolIndex, enterpriseIds, err := verticalWriter.decoder.DecodeStringValues(GetEncoding(valueBytes[1][0]), valueBytes[1][1:], string(keyBuffers[1]), store.GetName(), 0)

	assertions.Nil(err)

	assertions.Equal([]string{"0.0.1", "0.0.2"}, enterpriseIds)

	verticalWriter.decoder.MemoryPool.ReleaseStringPool(poolIndex)

	var messages []string

	event := DiskIOEvent{}

	waitGroup := &sync.WaitGroup{}

	blobEvent := datastore.BlobEvent{}

	blobEvent.ValueBytes = valueBytes[2][1:14]

	blobEvent.KeyBytes = keyBuffers[2]

	blobEvent.Encoder = verticalWriter.encoder

	blobEvent.Decoder = verticalWriter.decoder

	blobEvent.Store = store

	blobEvent.Tokenizer = mockMetricTokenizer

	blobEvent.DiskIOEvent = event

	blobEvent.WaitGroup = waitGroup

	blobEvent.Encoding = GetEncoding(valueBytes[2][0])

	poolIndex, message, err := datastore.GetBlobColumnValues(blobEvent)

	messages = append(messages, message[0])

	verticalWriter.decoder.MemoryPool.ReleaseStringPool(poolIndex)

	blobEvent.Encoding = GetEncoding(valueBytes[2][0])

	blobEvent.ValueBytes = valueBytes[2][14:27]

	poolIndex, message, err = datastore.GetBlobColumnValues(blobEvent)

	messages = append(messages, message[0])

	verticalWriter.decoder.MemoryPool.ReleaseStringPool(poolIndex)

	assertions.Nil(err)

	assertions.Equal([]string{"message1", "message2"}, messages)

	poolIndex, trapName, err := verticalWriter.decoder.DecodeStringValues(GetEncoding(valueBytes[3][0]), valueBytes[3][1:], string(keyBuffers[3]), store.GetName(), 0)

	assertions.Nil(err)

	assertions.Equal([]string{"trap1", "trap2"}, trapName)

	verticalWriter.decoder.MemoryPool.ReleaseStringPool(poolIndex)

	poolIndex, enterpriseTicks, err := datastore.GetTimeTicks(valueBytes[4], 0, verticalWriter.decoder)

	assertions.Nil(err)

	assertions.Equal([]int32{utils.UnixToSeconds(tick), utils.UnixToSeconds(tick)}, enterpriseTicks)

	verticalWriter.decoder.MemoryPool.ReleaseINT32Pool(poolIndex)

	poolIndex, enterpriseIdTicks, err := datastore.GetTimeTicks(valueBytes[5], 0, verticalWriter.decoder)

	assertions.Nil(err)

	assertions.Equal([]int32{utils.UnixToSeconds(tick), utils.UnixToSeconds(tick)}, enterpriseIdTicks)

	verticalWriter.decoder.MemoryPool.ReleaseINT32Pool(poolIndex)

	poolIndex, genericNameTicks, err := datastore.GetTimeTicks(valueBytes[6], 0, verticalWriter.decoder)

	assertions.Nil(err)

	assertions.Equal([]int32{utils.UnixToSeconds(tick), utils.UnixToSeconds(tick)}, genericNameTicks)

	verticalWriter.decoder.MemoryPool.ReleaseINT32Pool(poolIndex)

	poolIndex, trapNameTicks, err := datastore.GetTimeTicks(valueBytes[7], 0, verticalWriter.decoder)

	assertions.Nil(err)

	assertions.Equal([]int32{utils.UnixToSeconds(tick), utils.UnixToSeconds(tick)}, trapNameTicks)

	verticalWriter.decoder.MemoryPool.ReleaseINT32Pool(poolIndex)

}

//event writer test cases

func TestWriteStringFields(t *testing.T) {

	/*
			Step 1 :- Int32 and Int64 values

			 |          Columns              |                values          |
			 |   dummy.int8.column           |                  127           |
			 |   dummy.int16.column          |                  32767         |
			 |   dummy.int32.column          |                 213483647      |
		     |   dummy.int64.column          |          9223372036854775807   |

		   Step 2 :- String values for same column

		     |              Columns              |                values          |
			 |	    dummy.int8.column            |                 8%             |
			 |	    dummy.int16.column           |                 16%            |
		     |       dummy.int32.column          |                 32%            |
			 |       dummy.int64.column          |                 64%            |
		     |       dummy.string.column         |            stringType          |   New column

		Outcome:-

		minute^dummy.int8.column = ["127", "8%"]
		minute^dummy.int16.column = ["32767", "16%"]
		minute^dummy.int32.column = ["213483647", "32%"]
		minute^dummy.int64.column = ["9223372036854775807", "64%"]
		minute^dummy.string.column = ["", "stringType"]     // added extra empty string at position 0 in order to maintain the count of the array

	*/

	utils.CleanUpStores()

	assertions := assert.New(t)

	store := datastore.GetStore("14092022-dummy-column", utils.Log, true, true, encoder, mockMetricTokenizer)

	expectedValues := make(map[string][]string)

	batchTick := int64(1663146799)

	clear(horizontalWriter.int32Fields)

	clear(horizontalWriter.int64Fields)

	clear(horizontalWriter.stringFields)

	tick := utils.UnixToSeconds(batchTick)

	keys := []string{datastore.GetKey(tick, "dummy.int8.column", 0), datastore.GetKey(tick, "dummy.int16.column", 0), datastore.GetKey(tick, "dummy.int32.column", 0),
		datastore.GetKey(tick, "dummy.int64.column", 0), datastore.GetKey(tick, "dummy.string.column", 0), datastore.GetKey(tick, utils.EventSource, 0)}

	horizontalWriter.int32Fields["dummy.int8.column"] = []int32{int32(math.MaxInt8)}

	horizontalWriter.int32Fields["dummy.int8.column"+utils.KeySeparator+"max"] = []int32{int32(math.MaxInt8)}

	horizontalWriter.int32Fields["dummy.int16.column"] = []int32{int32(math.MaxInt16)}

	horizontalWriter.int32Fields["dummy.int16.column"+utils.KeySeparator+"max"] = []int32{int32(math.MaxInt16)}

	horizontalWriter.int32Fields["dummy.int32.column"] = []int32{int32(math.MaxInt32)}

	horizontalWriter.int32Fields["dummy.int32.column"+utils.KeySeparator+"max"] = []int32{int32(math.MaxInt32)}

	horizontalWriter.int64Fields["dummy.int64.column"] = []int64{int64(math.MaxInt64)}

	horizontalWriter.int64Fields["dummy.int64.column"+utils.KeySeparator+"max"] = []int64{int64(math.MaxInt64)}

	horizontalWriter.int64Fields["dummy.int64.column"+utils.KeySeparator+"max"] = []int64{int64(math.MaxInt64)}

	expectedValues[datastore.GetKey(tick, "dummy.int64.column", 0)] = []string{ToString(math.MaxInt64), "64%"}

	horizontalWriter.tick = utils.UnixToSeconds(batchTick)

	horizontalWriter.plugin = "dummy-column"

	horizontalWriter.batchSize = 1

	horizontalWriter.valueElementSize = 0

	horizontalWriter.txnPartition = store.GetPartition([]byte(datastore.GetKey(tick, "dummy.int64.column", 0)), tokenizer)

	err := horizontalWriter.writeFields(store, 0, horizontalWriter.batchSize, 0)

	assertions.Nil(err)

	copy(horizontalWriter.txnBufferBytes[horizontalWriter.txnOffset:], utils.EOTBytes)

	horizontalWriter.txnOffset += len(utils.EOTBytes)

	WriteINT32Value(int32(horizontalWriter.txnOffset-4), 0, horizontalWriter.txnBufferBytes)

	err = horizontalWriter.commitTxn(store, false, horizontalWriter.txnBufferBytes[:horizontalWriter.txnOffset], horizontalWriter.txnEntries, horizontalWriter.txnPartition)

	assertions.Nil(err)

	horizontalWriter.cleanupTxn()

	clear(horizontalWriter.int32Fields)

	clear(horizontalWriter.int64Fields)

	horizontalWriter.valueElementSize = 1

	horizontalWriter.stringFields["dummy.int8.column"] = []string{"8%"}

	expectedValues[datastore.GetKey(tick, "dummy.int8.column", 0)] = []string{ToString(math.MaxInt8), "8%"}

	horizontalWriter.stringFields["dummy.int16.column"] = []string{"16%"}

	expectedValues[datastore.GetKey(tick, "dummy.int16.column", 0)] = []string{ToString(math.MaxInt16), "16%"}

	horizontalWriter.stringFields["dummy.int32.column"] = []string{"32%"}

	expectedValues[datastore.GetKey(tick, "dummy.int32.column", 0)] = []string{ToString(math.MaxInt32), "32%"}

	horizontalWriter.stringFields["dummy.int64.column"] = []string{"64%"}

	horizontalWriter.stringFields["dummy.string.column"] = []string{"stringType"}

	horizontalWriter.stringFields[utils.EventSource] = []string{"************"}

	horizontalWriter.tick = utils.UnixToSeconds(batchTick)

	horizontalWriter.txnPartition = store.GetPartition([]byte(datastore.GetKey(tick, "dummy.int64.column", 0)), tokenizer)

	err = horizontalWriter.writeFields(store, 0, horizontalWriter.batchSize, 0)

	assertions.Nil(err)

	copy(horizontalWriter.txnBufferBytes[horizontalWriter.txnOffset:], utils.EOTBytes)

	horizontalWriter.txnOffset += len(utils.EOTBytes)

	WriteINT32Value(int32(horizontalWriter.txnOffset-4), 0, horizontalWriter.txnBufferBytes)

	err = horizontalWriter.commitTxn(store, false, horizontalWriter.txnBufferBytes[:horizontalWriter.txnOffset], horizontalWriter.txnEntries, horizontalWriter.txnPartition)

	assertions.Nil(err)

	horizontalWriter.cleanupTxn()

	expectedValues[datastore.GetKey(tick, "dummy.string.column", 0)] = []string{"", "stringType"}

	expectedValues[datastore.GetKey(tick, utils.EventSource, 0)] = []string{"", "************"}

	for _, key := range keys {

		found, valueBytes, err := store.Get([]byte(key), horizontalWriter.valueBytes, encoder, horizontalWriter.event, horizontalWriter.waitGroup, mockMetricTokenizer, true)

		assertions.True(found)

		assertions.Nil(err)

		assertions.NotNil(valueBytes)

		poolIndex, values, err := decoder.DecodeStringValues(GetEncoding(valueBytes[0]), valueBytes[1:], "", "", 0)

		assertions.Nil(err)

		assertions.EqualValues(expectedValues[key], values)

		decoder.MemoryPool.ReleaseStringPool(poolIndex)
	}

}

func TestDynamicBlobEncodingHorizontal(t *testing.T) {

	assertions := assert.New(t)

	store := datastore.GetStore("dynamic-blob-encoding-horizontal", utils.Log, true, true, encoder, mockMetricTokenizer)

	batchTick := int64(1663146799)

	clear(horizontalWriter.int32Fields)

	clear(horizontalWriter.int64Fields)

	clear(horizontalWriter.stringFields)

	tick := utils.UnixToSeconds(batchTick)

	datastore.AddBlobColumn(utils.Message)

	field := utils.Message

	keys := []string{datastore.GetKey(tick, field, 0), datastore.GetKey(tick+1, field, 0)}

	horizontalWriter.stringFields[field] = []string{utils.GenerateRandomString(800)}

	horizontalWriter.probes[field] = len(horizontalWriter.stringFields[utils.Message][0])

	horizontalWriter.tick = utils.UnixToSeconds(batchTick)

	horizontalWriter.plugin = "blob-encoding"

	horizontalWriter.batchSize = 1

	horizontalWriter.valueElementSize = 0

	horizontalWriter.txnPartition = store.GetPartition([]byte(datastore.GetKey(tick, field, 0)), tokenizer)

	err := horizontalWriter.writeFields(store, 0, horizontalWriter.batchSize, 0)

	assertions.Nil(err)

	copy(horizontalWriter.txnBufferBytes[horizontalWriter.txnOffset:], utils.EOTBytes)

	horizontalWriter.txnOffset += len(utils.EOTBytes)

	WriteINT32Value(int32(horizontalWriter.txnOffset-4), 0, horizontalWriter.txnBufferBytes)

	err = horizontalWriter.commitTxn(store, false, horizontalWriter.txnBufferBytes[:horizontalWriter.txnOffset], horizontalWriter.txnEntries, horizontalWriter.txnPartition)

	assertions.Nil(err)

	horizontalWriter.cleanupTxn()

	clear(horizontalWriter.probes)

	clear(horizontalWriter.stringFields)

	horizontalWriter.stringFields[field] = []string{utils.GenerateRandomString(100)}

	horizontalWriter.probes[field] = len(horizontalWriter.stringFields[utils.Message][0])

	horizontalWriter.valueElementSize = 1

	horizontalWriter.tick = utils.UnixToSeconds(batchTick)

	horizontalWriter.txnPartition = store.GetPartition([]byte(datastore.GetKey(tick, field, 0)), tokenizer)

	err = horizontalWriter.writeFields(store, 0, horizontalWriter.batchSize, 0)

	assertions.Nil(err)

	copy(horizontalWriter.txnBufferBytes[horizontalWriter.txnOffset:], utils.EOTBytes)

	horizontalWriter.txnOffset += len(utils.EOTBytes)

	WriteINT32Value(int32(horizontalWriter.txnOffset-4), 0, horizontalWriter.txnBufferBytes)

	err = horizontalWriter.commitTxn(store, false, horizontalWriter.txnBufferBytes[:horizontalWriter.txnOffset], horizontalWriter.txnEntries, horizontalWriter.txnPartition)

	assertions.Nil(err)

	horizontalWriter.cleanupTxn()

	clear(horizontalWriter.probes)

	clear(horizontalWriter.stringFields)

	horizontalWriter.stringFields[field] = []string{utils.GenerateRandomString(300)}

	horizontalWriter.probes[field] = len(horizontalWriter.stringFields[utils.Message][0])

	horizontalWriter.valueElementSize = 1

	horizontalWriter.tick = utils.UnixToSeconds(batchTick) + 1

	horizontalWriter.txnPartition = store.GetPartition([]byte(datastore.GetKey(horizontalWriter.tick, field, 0)), tokenizer)

	err = horizontalWriter.writeFields(store, 0, horizontalWriter.batchSize, 0)

	assertions.Nil(err)

	copy(horizontalWriter.txnBufferBytes[horizontalWriter.txnOffset:], utils.EOTBytes)

	horizontalWriter.txnOffset += len(utils.EOTBytes)

	WriteINT32Value(int32(horizontalWriter.txnOffset-4), 0, horizontalWriter.txnBufferBytes)

	err = horizontalWriter.commitTxn(store, false, horizontalWriter.txnBufferBytes[:horizontalWriter.txnOffset], horizontalWriter.txnEntries, horizontalWriter.txnPartition)

	assertions.Nil(err)

	horizontalWriter.cleanupTxn()

	for keyIndex, key := range keys {

		found, valueBytes, err := store.Get([]byte(key), horizontalWriter.valueBytes, encoder, horizontalWriter.event, horizontalWriter.waitGroup, mockMetricTokenizer, true)

		assertions.True(found)

		assertions.Nil(err)

		assertions.NotNil(valueBytes)

		if keyIndex == 0 {

			assertions.True(GetEncoding(valueBytes[0]) == Zstd)

		} else {

			assertions.True(GetEncoding(valueBytes[0]) == Snappy)
		}
	}
}

func TestWriteStringFieldsEventIdNotAvailable(t *testing.T) {

	utils.CleanUpStores()

	assertions := assert.New(t)

	store := datastore.GetStore("14092022-500000", utils.Log, true, true, encoder, mockMetricTokenizer)

	expectedValues := make(map[string][]string)

	batchTick := int64(1663146799)

	clear(horizontalWriter.int32Fields)

	clear(horizontalWriter.int64Fields)

	clear(horizontalWriter.stringFields)

	tick := utils.UnixToSeconds(batchTick)

	horizontalWriter.tick = utils.UnixToSeconds(batchTick)

	horizontalWriter.plugin = "dummy-int8-column"

	horizontalWriter.batchSize = 1

	keys := []string{datastore.GetKey(tick, "dummy.int8.column", 0), datastore.GetKey(tick, "dummy.int16.column", 0), datastore.GetKey(tick, "dummy.int32.column", 0),
		datastore.GetKey(tick, "dummy.int64.column", 0), datastore.GetKey(tick, "dummy.string.column", 0), datastore.GetKey(tick, utils.EventSource, 0)}

	horizontalWriter.int32Fields["dummy.int8.column"] = []int32{int32(math.MaxInt8)}

	horizontalWriter.int32Fields["dummy.int8.column"+utils.KeySeparator+"max"] = []int32{int32(math.MaxInt8)}

	horizontalWriter.int32Fields["dummy.int16.column"] = []int32{int32(math.MaxInt16)}

	horizontalWriter.int32Fields["dummy.int16.column"+utils.KeySeparator+"max"] = []int32{int32(math.MaxInt16)}

	horizontalWriter.int32Fields["dummy.int32.column"] = []int32{int32(math.MaxInt32)}

	horizontalWriter.int32Fields["dummy.int32.column"+utils.KeySeparator+"max"] = []int32{int32(math.MaxInt32)}

	horizontalWriter.int64Fields["dummy.int64.column"] = []int64{int64(math.MaxInt64)}

	horizontalWriter.int64Fields["dummy.int64.column"+utils.KeySeparator+"max"] = []int64{int64(math.MaxInt64)}

	horizontalWriter.valueElementSize = 0

	horizontalWriter.txnPartition = store.GetPartition([]byte(ToString(tick)+utils.KeySeparator+"dummy.int8.column"), tokenizer)

	err := horizontalWriter.writeFields(store, 0, horizontalWriter.batchSize, 0)

	assertions.Nil(err)

	copy(horizontalWriter.txnBufferBytes[horizontalWriter.txnOffset:], utils.EOTBytes)

	horizontalWriter.txnOffset += len(utils.EOTBytes)

	WriteINT32Value(int32(horizontalWriter.txnOffset-4), 0, horizontalWriter.txnBufferBytes)

	err = horizontalWriter.commitTxn(store, false, horizontalWriter.txnBufferBytes[:horizontalWriter.txnOffset], horizontalWriter.txnEntries, horizontalWriter.txnPartition)

	assertions.Nil(err)

	horizontalWriter.cleanupTxn()

	clear(horizontalWriter.stringFields)

	clear(horizontalWriter.int32Fields)

	clear(horizontalWriter.int64Fields)

	horizontalWriter.stringFields["dummy.int8.column"] = []string{"8%"}

	expectedValues[datastore.GetKey(tick, "dummy.int8.column", 0)] = []string{ToString(math.MaxInt8), "8%", "8%", "8%"}

	horizontalWriter.stringFields["dummy.int16.column"] = []string{"16%"}

	expectedValues[datastore.GetKey(tick, "dummy.int16.column", 0)] = []string{ToString(math.MaxInt16), "16%", "16%", "16%"}

	horizontalWriter.stringFields["dummy.int32.column"] = []string{"32%"}

	expectedValues[datastore.GetKey(tick, "dummy.int32.column", 0)] = []string{ToString(math.MaxInt32), "32%", "32%", "32%"}

	horizontalWriter.stringFields["dummy.int64.column"] = []string{"64%"}

	expectedValues[datastore.GetKey(tick, "dummy.int64.column", 0)] = []string{ToString(math.MaxInt64), "64%", "64%", "64%"}

	horizontalWriter.stringFields["dummy.string.column"] = []string{"stringType"}

	expectedValues[datastore.GetKey(tick, "dummy.string.column", 0)] = []string{"", "stringType", "stringType", "stringType"}

	horizontalWriter.stringFields[utils.EventSource] = []string{"************"}

	horizontalWriter.valueElementSize = 1

	horizontalWriter.txnPartition = store.GetPartition([]byte(ToString(tick)+utils.KeySeparator+"dummy.int8.column"), tokenizer)

	err = horizontalWriter.writeFields(store, 0, horizontalWriter.batchSize, 0)

	assertions.Nil(err)

	copy(horizontalWriter.txnBufferBytes[horizontalWriter.txnOffset:], utils.EOTBytes)

	horizontalWriter.txnOffset += len(utils.EOTBytes)

	WriteINT32Value(int32(horizontalWriter.txnOffset-4), 0, horizontalWriter.txnBufferBytes)

	err = horizontalWriter.commitTxn(store, false, horizontalWriter.txnBufferBytes[:horizontalWriter.txnOffset], horizontalWriter.txnEntries, horizontalWriter.txnPartition)

	assertions.Nil(err)

	horizontalWriter.cleanupTxn()

	expectedValues[datastore.GetKey(tick, utils.EventSource, 0)] = []string{"", "************", "************", "************"}

	horizontalWriter.valueElementSize = 2

	horizontalWriter.txnPartition = store.GetPartition([]byte(ToString(tick)+utils.KeySeparator+"dummy.int8.column"), tokenizer)

	err = horizontalWriter.writeFields(store, 0, horizontalWriter.batchSize, 0)

	copy(horizontalWriter.txnBufferBytes[horizontalWriter.txnOffset:], utils.EOTBytes)

	horizontalWriter.txnOffset += len(utils.EOTBytes)

	WriteINT32Value(int32(horizontalWriter.txnOffset-4), 0, horizontalWriter.txnBufferBytes)

	err = horizontalWriter.commitTxn(store, false, horizontalWriter.txnBufferBytes[:horizontalWriter.txnOffset], horizontalWriter.txnEntries, horizontalWriter.txnPartition)

	assertions.Nil(err)

	horizontalWriter.cleanupTxn()

	horizontalWriter.valueElementSize = 3

	horizontalWriter.txnPartition = store.GetPartition([]byte(ToString(tick)+utils.KeySeparator+"dummy.int8.column"), tokenizer)

	err = horizontalWriter.writeFields(store, 0, horizontalWriter.batchSize, 0)

	assertions.Nil(err)

	copy(horizontalWriter.txnBufferBytes[horizontalWriter.txnOffset:], utils.EOTBytes)

	horizontalWriter.txnOffset += len(utils.EOTBytes)

	WriteINT32Value(int32(horizontalWriter.txnOffset-4), 0, horizontalWriter.txnBufferBytes)

	err = horizontalWriter.commitTxn(store, false, horizontalWriter.txnBufferBytes[:horizontalWriter.txnOffset], horizontalWriter.txnEntries, horizontalWriter.txnPartition)

	assertions.Nil(err)

	horizontalWriter.cleanupTxn()

	for _, key := range keys {

		found, valueBytes, err := store.Get([]byte(key), horizontalWriter.valueBytes, encoder, horizontalWriter.event, horizontalWriter.waitGroup, mockMetricTokenizer, true)

		assertions.True(found)

		assertions.Nil(err)

		assertions.NotNil(valueBytes)

		poolIndex, values, err := decoder.DecodeStringValues(GetEncoding(valueBytes[0]), valueBytes[1:], "", "", 0)

		assertions.Nil(err)

		assertions.EqualValues(expectedValues[key], values)

		memoryPool.ReleaseStringPool(poolIndex)
	}

}

func TestWriteINT32Fields(t *testing.T) {

	/*
					Step 1 :- Int8 value

					 |          Columns              |                values          |
					 |   dummy.int.column            |                  127           |

			       Step 2 :- Int16 value

						 |          Columns              |                values          |
						 |   dummy.int.column            |                  32767          |

			      Step 3 :- Int32 value

		                 |          Columns              |                values          |
		                 |   dummy.int.column            |                  213483647      |

			      Step 4 :- Int64 value

								 |          Columns              |                values                    |
								 |   dummy.int.column            |                  9223372036854775807      |

			    Step 5 :- string value

								 |          Columns              |                values          |
								 |   dummy.int.column            |                  string%       |


				Outcome:-

				minute^dummy.int.column = ["127", "32767" ,"213483647","9223372036854775807","string%]
	*/

	assertions := assert.New(t)

	store := datastore.GetStore("14092022-dummy-int-column", utils.Log, true, true, encoder, mockMetricTokenizer)

	batchTick := int64(1663146799)

	horizontalWriter.batchSize = 1

	horizontalWriter.tick = utils.UnixToSeconds(batchTick)

	horizontalWriter.plugin = "dummy-int-column"

	tick := utils.UnixToSeconds(batchTick)

	clear(horizontalWriter.int32Fields)

	clear(horizontalWriter.int64Fields)

	clear(horizontalWriter.stringFields)

	expectedValues := []string{ToString(math.MaxInt8), ToString(math.MaxInt16), ToString(math.MaxInt32), ToString(math.MaxInt32), ToString(math.MaxInt64), ToString(math.MaxInt32), "string%", ToString(math.MaxInt32)}

	horizontalWriter.valueElementSize = 0

	horizontalWriter.int32Fields["dummy.int.column"+utils.KeySeparator+"max"] = []int32{int32(math.MaxInt8)}

	horizontalWriter.int32Fields["dummy.int.column"] = []int32{int32(math.MaxInt8)}

	horizontalWriter.txnPartition = store.GetPartition([]byte(datastore.GetKey(tick, "dummy.int64.column", 0)), tokenizer)

	err := horizontalWriter.writeFields(store, 0, horizontalWriter.batchSize, 0)

	copy(horizontalWriter.txnBufferBytes[horizontalWriter.txnOffset:], utils.EOTBytes)

	horizontalWriter.txnOffset += len(utils.EOTBytes)

	WriteINT32Value(int32(horizontalWriter.txnOffset-4), 0, horizontalWriter.txnBufferBytes)

	err = horizontalWriter.commitTxn(store, false, horizontalWriter.txnBufferBytes[:horizontalWriter.txnOffset], horizontalWriter.txnEntries, horizontalWriter.txnPartition)

	assertions.Nil(err)

	horizontalWriter.cleanupTxn()

	clear(horizontalWriter.stringFields)

	clear(horizontalWriter.int32Fields)

	clear(horizontalWriter.int64Fields)

	assertions.Nil(err)

	horizontalWriter.int32Fields["dummy.int.column"+utils.KeySeparator+"max"] = []int32{int32(math.MaxInt16)}

	horizontalWriter.int32Fields["dummy.int.column"] = []int32{int32(math.MaxInt16)}

	horizontalWriter.valueElementSize = 1

	horizontalWriter.txnPartition = store.GetPartition([]byte(datastore.GetKey(tick, "dummy.int64.column", 0)), tokenizer)

	err = horizontalWriter.writeFields(store, 0, horizontalWriter.batchSize, 0)

	copy(horizontalWriter.txnBufferBytes[horizontalWriter.txnOffset:], utils.EOTBytes)

	horizontalWriter.txnOffset += len(utils.EOTBytes)

	WriteINT32Value(int32(horizontalWriter.txnOffset-4), 0, horizontalWriter.txnBufferBytes)

	err = horizontalWriter.commitTxn(store, false, horizontalWriter.txnBufferBytes[:horizontalWriter.txnOffset], horizontalWriter.txnEntries, horizontalWriter.txnPartition)

	assertions.Nil(err)

	horizontalWriter.cleanupTxn()

	clear(horizontalWriter.stringFields)

	clear(horizontalWriter.int32Fields)

	clear(horizontalWriter.int64Fields)

	assertions.Nil(err)

	horizontalWriter.int32Fields["dummy.int.column"+utils.KeySeparator+"max"] = []int32{int32(math.MaxInt32)}

	horizontalWriter.int32Fields["dummy.int.column"] = []int32{int32(math.MaxInt32)}

	horizontalWriter.valueElementSize = 2

	horizontalWriter.txnPartition = store.GetPartition([]byte(datastore.GetKey(tick, "dummy.int64.column", 0)), tokenizer)

	err = horizontalWriter.writeFields(store, 0, horizontalWriter.batchSize, 0)

	copy(horizontalWriter.txnBufferBytes[horizontalWriter.txnOffset:], utils.EOTBytes)

	horizontalWriter.txnOffset += len(utils.EOTBytes)

	WriteINT32Value(int32(horizontalWriter.txnOffset-4), 0, horizontalWriter.txnBufferBytes)

	err = horizontalWriter.commitTxn(store, false, horizontalWriter.txnBufferBytes[:horizontalWriter.txnOffset], horizontalWriter.txnEntries, horizontalWriter.txnPartition)

	assertions.Nil(err)

	horizontalWriter.cleanupTxn()

	clear(horizontalWriter.stringFields)

	clear(horizontalWriter.int32Fields)

	clear(horizontalWriter.int64Fields)

	assertions.Nil(err)

	horizontalWriter.int32Fields["dummy.int.column"+utils.KeySeparator+"max"] = []int32{int32(math.MaxInt32)}

	horizontalWriter.int32Fields["dummy.int.column"] = []int32{int32(math.MaxInt32)}

	horizontalWriter.valueElementSize = 3

	horizontalWriter.txnPartition = store.GetPartition([]byte(datastore.GetKey(tick, "dummy.int64.column", 0)), tokenizer)

	err = horizontalWriter.writeFields(store, 0, horizontalWriter.batchSize, 0)

	copy(horizontalWriter.txnBufferBytes[horizontalWriter.txnOffset:], utils.EOTBytes)

	horizontalWriter.txnOffset += len(utils.EOTBytes)

	WriteINT32Value(int32(horizontalWriter.txnOffset-4), 0, horizontalWriter.txnBufferBytes)

	err = horizontalWriter.commitTxn(store, false, horizontalWriter.txnBufferBytes[:horizontalWriter.txnOffset], horizontalWriter.txnEntries, horizontalWriter.txnPartition)

	assertions.Nil(err)

	horizontalWriter.cleanupTxn()

	clear(horizontalWriter.stringFields)

	clear(horizontalWriter.int32Fields)

	clear(horizontalWriter.int64Fields)

	assertions.Nil(err)

	horizontalWriter.int64Fields["dummy.int.column"+utils.KeySeparator+"max"] = []int64{int64(math.MaxInt64)}

	horizontalWriter.int64Fields["dummy.int.column"] = []int64{int64(math.MaxInt64)}

	horizontalWriter.valueElementSize = 4

	horizontalWriter.txnPartition = store.GetPartition([]byte(datastore.GetKey(tick, "dummy.int64.column", 0)), tokenizer)

	err = horizontalWriter.writeFields(store, 0, horizontalWriter.batchSize, 0)

	copy(horizontalWriter.txnBufferBytes[horizontalWriter.txnOffset:], utils.EOTBytes)

	horizontalWriter.txnOffset += len(utils.EOTBytes)

	WriteINT32Value(int32(horizontalWriter.txnOffset-4), 0, horizontalWriter.txnBufferBytes)

	err = horizontalWriter.commitTxn(store, false, horizontalWriter.txnBufferBytes[:horizontalWriter.txnOffset], horizontalWriter.txnEntries, horizontalWriter.txnPartition)

	assertions.Nil(err)

	horizontalWriter.cleanupTxn()

	clear(horizontalWriter.stringFields)

	clear(horizontalWriter.int32Fields)

	clear(horizontalWriter.int64Fields)

	assertions.Nil(err)

	horizontalWriter.int32Fields["dummy.int.column"+utils.KeySeparator+"max"] = []int32{int32(math.MaxInt32)}

	horizontalWriter.int32Fields["dummy.int.column"] = []int32{int32(math.MaxInt32)}

	horizontalWriter.valueElementSize = 5

	horizontalWriter.txnPartition = store.GetPartition([]byte(datastore.GetKey(tick, "dummy.int64.column", 0)), tokenizer)

	err = horizontalWriter.writeFields(store, 0, horizontalWriter.batchSize, 0)

	copy(horizontalWriter.txnBufferBytes[horizontalWriter.txnOffset:], utils.EOTBytes)

	horizontalWriter.txnOffset += len(utils.EOTBytes)

	WriteINT32Value(int32(horizontalWriter.txnOffset-4), 0, horizontalWriter.txnBufferBytes)

	err = horizontalWriter.commitTxn(store, false, horizontalWriter.txnBufferBytes[:horizontalWriter.txnOffset], horizontalWriter.txnEntries, horizontalWriter.txnPartition)

	assertions.Nil(err)

	horizontalWriter.cleanupTxn()

	clear(horizontalWriter.stringFields)

	clear(horizontalWriter.int32Fields)

	clear(horizontalWriter.int64Fields)

	assertions.Nil(err)

	horizontalWriter.stringFields["dummy.int.column"+utils.KeySeparator+"max"] = []string{"string%"}

	horizontalWriter.stringFields["dummy.int.column"] = []string{"string%"}

	horizontalWriter.valueElementSize = 6

	horizontalWriter.txnPartition = store.GetPartition([]byte(datastore.GetKey(tick, "dummy.int64.column", 0)), tokenizer)

	err = horizontalWriter.writeFields(store, 0, horizontalWriter.batchSize, 0)

	copy(horizontalWriter.txnBufferBytes[horizontalWriter.txnOffset:], utils.EOTBytes)

	horizontalWriter.txnOffset += len(utils.EOTBytes)

	WriteINT32Value(int32(horizontalWriter.txnOffset-4), 0, horizontalWriter.txnBufferBytes)

	err = horizontalWriter.commitTxn(store, false, horizontalWriter.txnBufferBytes[:horizontalWriter.txnOffset], horizontalWriter.txnEntries, horizontalWriter.txnPartition)

	assertions.Nil(err)

	horizontalWriter.cleanupTxn()

	clear(horizontalWriter.stringFields)

	clear(horizontalWriter.int32Fields)

	clear(horizontalWriter.int64Fields)

	assertions.Nil(err)

	horizontalWriter.int32Fields["dummy.int.column"+utils.KeySeparator+"max"] = []int32{int32(math.MaxInt32)}

	horizontalWriter.int32Fields["dummy.int.column"] = []int32{int32(math.MaxInt32)}

	horizontalWriter.valueElementSize = 7

	horizontalWriter.txnPartition = store.GetPartition([]byte(datastore.GetKey(tick, "dummy.int64.column", 0)), tokenizer)

	err = horizontalWriter.writeFields(store, 0, horizontalWriter.batchSize, 0)

	copy(horizontalWriter.txnBufferBytes[horizontalWriter.txnOffset:], utils.EOTBytes)

	horizontalWriter.txnOffset += len(utils.EOTBytes)

	WriteINT32Value(int32(horizontalWriter.txnOffset-4), 0, horizontalWriter.txnBufferBytes)

	err = horizontalWriter.commitTxn(store, false, horizontalWriter.txnBufferBytes[:horizontalWriter.txnOffset], horizontalWriter.txnEntries, horizontalWriter.txnPartition)

	assertions.Nil(err)

	horizontalWriter.cleanupTxn()

	clear(horizontalWriter.stringFields)

	clear(horizontalWriter.int32Fields)

	clear(horizontalWriter.int64Fields)

	assertions.Nil(err)

	key := datastore.GetKey(tick, "dummy.int.column", 0)

	found, valueBytes, err := store.Get([]byte(key), horizontalWriter.valueBytes, encoder, horizontalWriter.event, horizontalWriter.waitGroup, mockMetricTokenizer, true)

	assertions.True(found)

	assertions.Nil(err)

	assertions.NotNil(valueBytes)

	poolIndex, values, err := decoder.DecodeStringValues(GetEncoding(valueBytes[0]), valueBytes[1:], "", "", 0)

	assertions.Nil(err)

	assertions.EqualValues(expectedValues, values)

	decoder.MemoryPool.ReleaseStringPool(poolIndex)

}

// When we convert int32 to int64 and if previous datatype is int24 in that case datatype(Int64) is passed because of which error occurs. (failed to read uint32/int32 values)
func TestWriteINT32FieldsType1(t *testing.T) {

	/*
				Step 1 :- Int24 value

				 |          Columns              |                values          |
				 |   dummy.int.column            |                  MaxInt24           |

		      Step 4 :- Int64 value

							 |          Columns              |                values                    |
							 |   dummy.int.column            |                  9223372036854775807      |


			Outcome:-

			minute^dummy.int.column = ["maxInt24","9223372036854775807"]
	*/

	assertions := assert.New(t)

	store := datastore.GetStore("14092022-dummy-int-column1", utils.Log, true, true, encoder, mockMetricTokenizer)

	batchTick := int64(1663146799)

	horizontalWriter.batchSize = 1

	horizontalWriter.tick = utils.UnixToSeconds(batchTick)

	horizontalWriter.plugin = "dummy-int-column"

	tick := utils.UnixToSeconds(batchTick)

	clear(horizontalWriter.int32Fields)

	clear(horizontalWriter.int64Fields)

	clear(horizontalWriter.stringFields)

	expectedValues := []int64{int64(MaxInt24), math.MaxInt64}

	horizontalWriter.valueElementSize = 0

	horizontalWriter.int32Fields["dummy.int.column"+utils.KeySeparator+"max"] = []int32{int32(MaxInt24)}

	horizontalWriter.int32Fields["dummy.int.column"] = []int32{int32(MaxInt24)}

	horizontalWriter.txnPartition = store.GetPartition([]byte(datastore.GetKey(tick, "dummy.int64.column", 0)), tokenizer)

	err := horizontalWriter.writeFields(store, 0, horizontalWriter.batchSize, 0)

	copy(horizontalWriter.txnBufferBytes[horizontalWriter.txnOffset:], utils.EOTBytes)

	horizontalWriter.txnOffset += len(utils.EOTBytes)

	WriteINT32Value(int32(horizontalWriter.txnOffset-4), 0, horizontalWriter.txnBufferBytes)

	err = horizontalWriter.commitTxn(store, false, horizontalWriter.txnBufferBytes[:horizontalWriter.txnOffset], horizontalWriter.txnEntries, horizontalWriter.txnPartition)

	assertions.Nil(err)

	horizontalWriter.cleanupTxn()

	clear(horizontalWriter.stringFields)

	clear(horizontalWriter.int32Fields)

	clear(horizontalWriter.int64Fields)

	assertions.Nil(err)

	horizontalWriter.int64Fields["dummy.int.column"+utils.KeySeparator+"max"] = []int64{int64(math.MaxInt64)}

	horizontalWriter.int64Fields["dummy.int.column"] = []int64{int64(math.MaxInt64)}

	horizontalWriter.valueElementSize = 1

	horizontalWriter.txnPartition = store.GetPartition([]byte(datastore.GetKey(tick, "dummy.int64.column", 0)), tokenizer)

	err = horizontalWriter.writeFields(store, 0, horizontalWriter.batchSize, 0)

	copy(horizontalWriter.txnBufferBytes[horizontalWriter.txnOffset:], utils.EOTBytes)

	horizontalWriter.txnOffset += len(utils.EOTBytes)

	WriteINT32Value(int32(horizontalWriter.txnOffset-4), 0, horizontalWriter.txnBufferBytes)

	err = horizontalWriter.commitTxn(store, false, horizontalWriter.txnBufferBytes[:horizontalWriter.txnOffset], horizontalWriter.txnEntries, horizontalWriter.txnPartition)

	assertions.Nil(err)

	horizontalWriter.cleanupTxn()

	clear(horizontalWriter.stringFields)

	clear(horizontalWriter.int32Fields)

	clear(horizontalWriter.int64Fields)

	assertions.Nil(err)

	key := datastore.GetKey(tick, "dummy.int.column", 0)

	found, valueBytes, err := store.Get([]byte(key), horizontalWriter.valueBytes, encoder, horizontalWriter.event, horizontalWriter.waitGroup, mockMetricTokenizer, true)

	assertions.True(found)

	assertions.Nil(err)

	assertions.NotNil(valueBytes)

	poolIndex, values, err := decoder.DecodeINT64Values(GetEncoding(valueBytes[0]), Int64, valueBytes[1:], "", "", 0)

	assertions.Nil(err)

	assertions.EqualValues(expectedValues, values)

	decoder.MemoryPool.ReleaseStringPool(poolIndex)

}

func TestWriteINT64Fields(t *testing.T) {

	/*
					Step 1 :- Int32 and Int64 values

					 |          Columns              |                values          |
					 |   dummy.int64.column          |   9223372036854775807          |
					 |   dummy.string.column         |                  5%            |

				   Step 2 :- Int32 values

			         |          Columns              |                values          |
					 |   dummy.int64.column          |   9223372036854775807          |
					 |   dummy.int8.column          |                  127            |
					 |   dummy.int16.column          |                  32767         |
					 |   dummy.int32.column          |                  213483647     |


		         Step 3 :- Int64 columns

				         |          Columns              |                values          |
						 |   dummy.int64.column          |   9223372036854775807          |
						 |   dummy.int8.column          |    9223372036854775807                          |
						 |   dummy.int16.column          |   9223372036854775807          |
						 |   dummy.int32.column          |   9223372036854775807          |
						 |   dummy.string.column          |   9223372036854775807          |

					Outcome:-

				minute^dummy.int8.column = [0,"127", "9223372036854775807"]
				minute^dummy.int16.column = [0,"32767", "9223372036854775807"]
				minute^dummy.int32.column = [0,"213483647", "9223372036854775807 "]
				minute^dummy.int64.column = ["9223372036854775807", "9223372036854775807","9223372036854775807"]
				minute^dummy.string.column = ["5%", "", "9223372036854775807]     // added extra empty string at position 0 in order to maintain the count of the array

	*/

	assertions := assert.New(t)

	store := datastore.GetStore("14092022-dummy-int64-column", utils.Log, true, true, encoder, mockMetricTokenizer)

	batchTick := int64(1663146799)

	clear(horizontalWriter.int32Fields)

	clear(horizontalWriter.int64Fields)

	clear(horizontalWriter.stringFields)

	tick := utils.UnixToSeconds(batchTick)

	horizontalWriter.plugin = "dummy-int64-column"

	horizontalWriter.batchSize = 1

	horizontalWriter.tick = utils.UnixToSeconds(batchTick)

	keys := []string{datastore.GetKey(tick, "dummy.int8.column", 0), datastore.GetKey(tick, "dummy.int16.column", 0), datastore.GetKey(tick, "dummy.int32.column", 0),
		datastore.GetKey(tick, "dummy.int64.column", 0), datastore.GetKey(tick, "dummy.string.column", 0)}

	expectedINT64Values := make(map[string][]int64)

	expectedStringValues := make(map[string][]string)

	horizontalWriter.int64Fields["dummy.int64.column"+utils.KeySeparator+"max"] = []int64{int64(math.MaxInt64)}

	horizontalWriter.int64Fields["dummy.int64.column"] = []int64{int64(math.MaxInt64)}

	horizontalWriter.stringFields["dummy.string.column"] = []string{"5%"}

	horizontalWriter.valueElementSize = 0

	horizontalWriter.txnPartition = store.GetPartition([]byte(ToString(tick)+utils.KeySeparator+"dummy.int64.column"), tokenizer)

	err := horizontalWriter.writeFields(store, 0, horizontalWriter.batchSize, 0)

	copy(horizontalWriter.txnBufferBytes[horizontalWriter.txnOffset:], utils.EOTBytes)

	horizontalWriter.txnOffset += len(utils.EOTBytes)

	WriteINT32Value(int32(horizontalWriter.txnOffset-4), 0, horizontalWriter.txnBufferBytes)

	err = horizontalWriter.commitTxn(store, false, horizontalWriter.txnBufferBytes[:horizontalWriter.txnOffset], horizontalWriter.txnEntries, horizontalWriter.txnPartition)

	assertions.Nil(err)

	horizontalWriter.cleanupTxn()

	clear(horizontalWriter.stringFields)

	clear(horizontalWriter.int32Fields)

	assertions.Nil(err)

	horizontalWriter.int32Fields["dummy.int8.column"+utils.KeySeparator+"max"] = []int32{int32(math.MaxInt8)}

	horizontalWriter.int32Fields["dummy.int8.column"] = []int32{int32(math.MaxInt8)}

	horizontalWriter.int32Fields["dummy.int16.column"+utils.KeySeparator+"max"] = []int32{int32(math.MaxInt16)}

	horizontalWriter.int32Fields["dummy.int16.column"] = []int32{int32(math.MaxInt16)}

	horizontalWriter.int32Fields["dummy.int32.column"+utils.KeySeparator+"max"] = []int32{int32(math.MaxInt32)}

	horizontalWriter.int32Fields["dummy.int32.column"] = []int32{int32(math.MaxInt32)}

	horizontalWriter.valueElementSize = 1

	horizontalWriter.txnPartition = store.GetPartition([]byte(ToString(tick)+utils.KeySeparator+"dummy.int64.column"), tokenizer)

	err = horizontalWriter.writeFields(store, 0, horizontalWriter.batchSize, 0)

	copy(horizontalWriter.txnBufferBytes[horizontalWriter.txnOffset:], utils.EOTBytes)

	horizontalWriter.txnOffset += len(utils.EOTBytes)

	WriteINT32Value(int32(horizontalWriter.txnOffset-4), 0, horizontalWriter.txnBufferBytes)

	err = horizontalWriter.commitTxn(store, false, horizontalWriter.txnBufferBytes[:horizontalWriter.txnOffset], horizontalWriter.txnEntries, horizontalWriter.txnPartition)

	assertions.Nil(err)

	horizontalWriter.cleanupTxn()

	clear(horizontalWriter.stringFields)

	clear(horizontalWriter.int32Fields)

	horizontalWriter.int64Fields["dummy.int8.column"+utils.KeySeparator+"max"] = []int64{int64(math.MaxInt64)}

	horizontalWriter.int64Fields["dummy.int8.column"] = []int64{int64(math.MaxInt64)}

	expectedINT64Values[datastore.GetKey(tick, "dummy.int8.column", 0)] = []int64{0, math.MaxInt8, math.MaxInt64}

	horizontalWriter.int64Fields["dummy.int16.column"] = []int64{int64(math.MaxInt64)}

	horizontalWriter.int64Fields["dummy.int16.column"+utils.KeySeparator+"max"] = []int64{int64(math.MaxInt64)}

	expectedINT64Values[datastore.GetKey(tick, "dummy.int16.column", 0)] = []int64{0, math.MaxInt16, math.MaxInt64}

	horizontalWriter.int64Fields["dummy.int32.column"] = []int64{int64(math.MaxInt64)}

	horizontalWriter.int64Fields["dummy.int32.column"+utils.KeySeparator+"max"] = []int64{int64(math.MaxInt64)}

	expectedINT64Values[datastore.GetKey(tick, "dummy.int32.column", 0)] = []int64{0, math.MaxInt32, math.MaxInt64}

	expectedINT64Values[datastore.GetKey(tick, "dummy.int64.column", 0)] = []int64{math.MaxInt64, math.MaxInt64, math.MaxInt64}

	horizontalWriter.int64Fields["dummy.string.column"] = []int64{int64(math.MaxInt64)}

	horizontalWriter.int64Fields["dummy.string.column"+utils.KeySeparator+"max"] = []int64{int64(math.MaxInt64)}

	expectedStringValues[datastore.GetKey(tick, "dummy.string.column", 0)] = []string{"5%", "", ToString(math.MaxInt64)}

	horizontalWriter.valueElementSize = 2

	horizontalWriter.txnPartition = store.GetPartition([]byte(ToString(tick)+utils.KeySeparator+"dummy.int64.column"), tokenizer)

	err = horizontalWriter.writeFields(store, 0, horizontalWriter.batchSize, 0)

	copy(horizontalWriter.txnBufferBytes[horizontalWriter.txnOffset:], utils.EOTBytes)

	horizontalWriter.txnOffset += len(utils.EOTBytes)

	WriteINT32Value(int32(horizontalWriter.txnOffset-4), 0, horizontalWriter.txnBufferBytes)

	err = horizontalWriter.commitTxn(store, false, horizontalWriter.txnBufferBytes[:horizontalWriter.txnOffset], horizontalWriter.txnEntries, horizontalWriter.txnPartition)

	assertions.Nil(err)

	horizontalWriter.cleanupTxn()

	clear(horizontalWriter.stringFields)

	clear(horizontalWriter.int32Fields)

	clear(horizontalWriter.int64Fields)

	assertions.Nil(err)

	for _, key := range keys {

		found, valueBytes, err := store.Get([]byte(key), horizontalWriter.valueBytes, encoder, horizontalWriter.event, horizontalWriter.waitGroup, mockMetricTokenizer, true)

		assertions.True(found)

		assertions.Nil(err)

		assertions.NotNil(valueBytes)

		if strings.Contains(key, "string") {

			poolIndex, values, err := decoder.DecodeStringValues(GetEncoding(valueBytes[0]), valueBytes[1:], "", "", 0)

			assertions.Nil(err)

			assertions.EqualValues(expectedStringValues[key], values)

			decoder.MemoryPool.ReleaseStringPool(poolIndex)

		} else {

			poolIndex, _, _, _, _, values, _, _, err := datastore.GetColumns(GetDataType(valueBytes[0]), valueBytes[0], valueBytes[1:], "", "", 0, decoder)

			assertions.Nil(err)

			assertions.EqualValues(expectedINT64Values[key], values)

			decoder.MemoryPool.ReleaseINT64Pool(poolIndex)

		}

	}

}

func TestWriteINT64FieldsType1(t *testing.T) {

	assertions := assert.New(t)

	store := datastore.GetStore("14092022-dummy-int64-column1", utils.Log, true, true, encoder, mockMetricTokenizer)

	batchTick := int64(1663146799)

	horizontalWriter.batchSize = 1

	horizontalWriter.tick = utils.UnixToSeconds(batchTick)

	horizontalWriter.plugin = "dummy-int-column"

	tick := utils.UnixToSeconds(batchTick)

	clear(horizontalWriter.int32Fields)

	clear(horizontalWriter.int64Fields)

	clear(horizontalWriter.stringFields)

	expectedValues := []string{"0", "0", "0", "0", "9223372036854775807", "2147483647", "string%", "2147483647"}

	horizontalWriter.valueElementSize = 0

	horizontalWriter.int64Fields["dummy.int.column"+utils.KeySeparator+"max"] = []int64{int64(math.MaxInt8)}

	horizontalWriter.int64Fields["dummy.int.column"] = []int64{int64(math.MaxInt8)}

	horizontalWriter.txnPartition = store.GetPartition([]byte(datastore.GetKey(tick, "dummy.int64.column", 0)), tokenizer)

	err := horizontalWriter.writeFields(store, 0, horizontalWriter.batchSize, 0)

	copy(horizontalWriter.txnBufferBytes[horizontalWriter.txnOffset:], utils.EOTBytes)

	horizontalWriter.txnOffset += len(utils.EOTBytes)

	WriteINT32Value(int32(horizontalWriter.txnOffset-4), 0, horizontalWriter.txnBufferBytes)

	err = horizontalWriter.commitTxn(store, false, horizontalWriter.txnBufferBytes[:horizontalWriter.txnOffset], horizontalWriter.txnEntries, horizontalWriter.txnPartition)

	assertions.Nil(err)

	horizontalWriter.cleanupTxn()

	clear(horizontalWriter.stringFields)

	clear(horizontalWriter.int32Fields)

	clear(horizontalWriter.int64Fields)

	assertions.Nil(err)

	horizontalWriter.int64Fields["dummy.int.column"+utils.KeySeparator+"max"] = []int64{int64(math.MaxInt16)}

	horizontalWriter.int64Fields["dummy.int.column"] = []int64{int64(math.MaxInt16)}

	horizontalWriter.valueElementSize = 1

	horizontalWriter.txnPartition = store.GetPartition([]byte(datastore.GetKey(tick, "dummy.int64.column", 0)), tokenizer)

	err = horizontalWriter.writeFields(store, 0, horizontalWriter.batchSize, 0)

	copy(horizontalWriter.txnBufferBytes[horizontalWriter.txnOffset:], utils.EOTBytes)

	horizontalWriter.txnOffset += len(utils.EOTBytes)

	WriteINT32Value(int32(horizontalWriter.txnOffset-4), 0, horizontalWriter.txnBufferBytes)

	err = horizontalWriter.commitTxn(store, false, horizontalWriter.txnBufferBytes[:horizontalWriter.txnOffset], horizontalWriter.txnEntries, horizontalWriter.txnPartition)

	assertions.Nil(err)

	horizontalWriter.cleanupTxn()

	clear(horizontalWriter.stringFields)

	clear(horizontalWriter.int32Fields)

	clear(horizontalWriter.int64Fields)

	assertions.Nil(err)

	horizontalWriter.int64Fields["dummy.int.column"+utils.KeySeparator+"max"] = []int64{int64(math.MaxInt32)}

	horizontalWriter.int64Fields["dummy.int.column"] = []int64{int64(math.MaxInt32)}

	horizontalWriter.valueElementSize = 2

	horizontalWriter.txnPartition = store.GetPartition([]byte(datastore.GetKey(tick, "dummy.int64.column", 0)), tokenizer)

	err = horizontalWriter.writeFields(store, 0, horizontalWriter.batchSize, 0)

	copy(horizontalWriter.txnBufferBytes[horizontalWriter.txnOffset:], utils.EOTBytes)

	horizontalWriter.txnOffset += len(utils.EOTBytes)

	WriteINT32Value(int32(horizontalWriter.txnOffset-4), 0, horizontalWriter.txnBufferBytes)

	err = horizontalWriter.commitTxn(store, false, horizontalWriter.txnBufferBytes[:horizontalWriter.txnOffset], horizontalWriter.txnEntries, horizontalWriter.txnPartition)

	assertions.Nil(err)

	horizontalWriter.cleanupTxn()

	clear(horizontalWriter.stringFields)

	clear(horizontalWriter.int32Fields)

	clear(horizontalWriter.int64Fields)

	assertions.Nil(err)

	horizontalWriter.int64Fields["dummy.int.column"+utils.KeySeparator+"max"] = []int64{int64(math.MaxInt32)}

	horizontalWriter.int64Fields["dummy.int.column"] = []int64{int64(math.MaxInt32)}

	horizontalWriter.valueElementSize = 3

	horizontalWriter.txnPartition = store.GetPartition([]byte(datastore.GetKey(tick, "dummy.int64.column", 0)), tokenizer)

	err = horizontalWriter.writeFields(store, 0, horizontalWriter.batchSize, 0)

	copy(horizontalWriter.txnBufferBytes[horizontalWriter.txnOffset:], utils.EOTBytes)

	horizontalWriter.txnOffset += len(utils.EOTBytes)

	WriteINT32Value(int32(horizontalWriter.txnOffset-4), 0, horizontalWriter.txnBufferBytes)

	err = horizontalWriter.commitTxn(store, false, horizontalWriter.txnBufferBytes[:horizontalWriter.txnOffset], horizontalWriter.txnEntries, horizontalWriter.txnPartition)

	assertions.Nil(err)

	horizontalWriter.cleanupTxn()

	clear(horizontalWriter.stringFields)

	clear(horizontalWriter.int32Fields)

	clear(horizontalWriter.int64Fields)

	assertions.Nil(err)

	horizontalWriter.int64Fields["dummy.int.column"+utils.KeySeparator+"max"] = []int64{int64(math.MaxInt64)}

	horizontalWriter.int64Fields["dummy.int.column"] = []int64{int64(math.MaxInt64)}

	horizontalWriter.valueElementSize = 4

	horizontalWriter.txnPartition = store.GetPartition([]byte(datastore.GetKey(tick, "dummy.int64.column", 0)), tokenizer)

	err = horizontalWriter.writeFields(store, 0, horizontalWriter.batchSize, 0)

	copy(horizontalWriter.txnBufferBytes[horizontalWriter.txnOffset:], utils.EOTBytes)

	horizontalWriter.txnOffset += len(utils.EOTBytes)

	WriteINT32Value(int32(horizontalWriter.txnOffset-4), 0, horizontalWriter.txnBufferBytes)

	err = horizontalWriter.commitTxn(store, false, horizontalWriter.txnBufferBytes[:horizontalWriter.txnOffset], horizontalWriter.txnEntries, horizontalWriter.txnPartition)

	assertions.Nil(err)

	horizontalWriter.cleanupTxn()

	clear(horizontalWriter.stringFields)

	clear(horizontalWriter.int32Fields)

	clear(horizontalWriter.int64Fields)

	assertions.Nil(err)

	horizontalWriter.int64Fields["dummy.int.column"+utils.KeySeparator+"max"] = []int64{int64(math.MaxInt32)}

	horizontalWriter.int64Fields["dummy.int.column"] = []int64{int64(math.MaxInt32)}

	horizontalWriter.valueElementSize = 5

	horizontalWriter.txnPartition = store.GetPartition([]byte(datastore.GetKey(tick, "dummy.int64.column", 0)), tokenizer)

	err = horizontalWriter.writeFields(store, 0, horizontalWriter.batchSize, 0)

	copy(horizontalWriter.txnBufferBytes[horizontalWriter.txnOffset:], utils.EOTBytes)

	horizontalWriter.txnOffset += len(utils.EOTBytes)

	WriteINT32Value(int32(horizontalWriter.txnOffset-4), 0, horizontalWriter.txnBufferBytes)

	err = horizontalWriter.commitTxn(store, false, horizontalWriter.txnBufferBytes[:horizontalWriter.txnOffset], horizontalWriter.txnEntries, horizontalWriter.txnPartition)

	assertions.Nil(err)

	horizontalWriter.cleanupTxn()

	clear(horizontalWriter.stringFields)

	clear(horizontalWriter.int32Fields)

	clear(horizontalWriter.int64Fields)

	assertions.Nil(err)

	horizontalWriter.stringFields["dummy.int.column"+utils.KeySeparator+"max"] = []string{"string%"}

	horizontalWriter.stringFields["dummy.int.column"] = []string{"string%"}

	horizontalWriter.valueElementSize = 6

	horizontalWriter.txnPartition = store.GetPartition([]byte(datastore.GetKey(tick, "dummy.int64.column", 0)), tokenizer)

	err = horizontalWriter.writeFields(store, 0, horizontalWriter.batchSize, 0)

	copy(horizontalWriter.txnBufferBytes[horizontalWriter.txnOffset:], utils.EOTBytes)

	horizontalWriter.txnOffset += len(utils.EOTBytes)

	WriteINT32Value(int32(horizontalWriter.txnOffset-4), 0, horizontalWriter.txnBufferBytes)

	err = horizontalWriter.commitTxn(store, false, horizontalWriter.txnBufferBytes[:horizontalWriter.txnOffset], horizontalWriter.txnEntries, horizontalWriter.txnPartition)

	assertions.Nil(err)

	horizontalWriter.cleanupTxn()

	clear(horizontalWriter.stringFields)

	clear(horizontalWriter.int32Fields)

	clear(horizontalWriter.int64Fields)

	assertions.Nil(err)

	horizontalWriter.int64Fields["dummy.int.column"+utils.KeySeparator+"max"] = []int64{int64(math.MaxInt32)}

	horizontalWriter.int64Fields["dummy.int.column"] = []int64{int64(math.MaxInt32)}

	horizontalWriter.valueElementSize = 7

	horizontalWriter.txnPartition = store.GetPartition([]byte(datastore.GetKey(tick, "dummy.int64.column", 0)), tokenizer)

	err = horizontalWriter.writeFields(store, 0, horizontalWriter.batchSize, 0)

	copy(horizontalWriter.txnBufferBytes[horizontalWriter.txnOffset:], utils.EOTBytes)

	horizontalWriter.txnOffset += len(utils.EOTBytes)

	WriteINT32Value(int32(horizontalWriter.txnOffset-4), 0, horizontalWriter.txnBufferBytes)

	err = horizontalWriter.commitTxn(store, false, horizontalWriter.txnBufferBytes[:horizontalWriter.txnOffset], horizontalWriter.txnEntries, horizontalWriter.txnPartition)

	assertions.Nil(err)

	horizontalWriter.cleanupTxn()

	clear(horizontalWriter.stringFields)

	clear(horizontalWriter.int32Fields)

	clear(horizontalWriter.int64Fields)

	assertions.Nil(err)

	key := datastore.GetKey(tick, "dummy.int.column", 0)

	found, valueBytes, err := store.Get([]byte(key), horizontalWriter.valueBytes, encoder, horizontalWriter.event, horizontalWriter.waitGroup, mockMetricTokenizer, true)

	assertions.True(found)

	assertions.Nil(err)

	assertions.NotNil(valueBytes)

	poolIndex, values, err := decoder.DecodeStringValues(GetEncoding(valueBytes[0]), valueBytes[1:], "", "", 0)

	assertions.Nil(err)

	assertions.EqualValues(expectedValues, values)

	decoder.MemoryPool.ReleaseStringPool(poolIndex)

}

// BugID :- 24926

/*
Bug description :- if the event count is smaller than the decoded values then probably the count was not updated
and the datastore was shutdown before that hence in that case we need to consider the previous event count value and append
next value after it.
*/
func TestWriteEventsMissingFields(t *testing.T) {

	store := datastore.GetStore("missing-fields", utils.Log, true, true, encoder, mockMetricTokenizer)

	horizontalWriter.clearContext()

	horizontalWriter.batchSize = 2

	tick := utils.UnixToSeconds(1663146799)

	horizontalWriter.tick = tick

	horizontalWriter.plugin = "missing-fields"

	horizontalWriter.valueElementSize = 0

	horizontalWriter.int64Fields["int64field1"] = append(horizontalWriter.int64Fields["int64field1"], math.MaxInt32+1, math.MaxInt32+2)

	horizontalWriter.int64Fields["int64field1^max"] = append(horizontalWriter.int64Fields["int64field1^max"], math.MaxInt32+2)

	horizontalWriter.int64Fields["int64field2"] = append(horizontalWriter.int64Fields["int64field2"], math.MaxInt32+1, math.MaxInt32+2)

	horizontalWriter.int64Fields["int64field2^max"] = append(horizontalWriter.int64Fields["int64field2^max"], math.MaxInt32+2)

	horizontalWriter.txnPartition = store.GetPartition([]byte(datastore.GetKey(tick, "dummy.int64.column", 0)), tokenizer)

	err := horizontalWriter.writeFields(store, 0, horizontalWriter.batchSize, 0)

	copy(horizontalWriter.txnBufferBytes[horizontalWriter.txnOffset:], utils.EOTBytes)

	horizontalWriter.txnOffset += len(utils.EOTBytes)

	WriteINT32Value(int32(horizontalWriter.txnOffset-4), 0, horizontalWriter.txnBufferBytes)

	err = horizontalWriter.commitTxn(store, false, horizontalWriter.txnBufferBytes[:horizontalWriter.txnOffset], horizontalWriter.txnEntries, horizontalWriter.txnPartition)

	horizontalWriter.cleanupTxn()

	assertions := assert.New(t)

	assertions.Nil(err)

	horizontalWriter.clearContext()

	horizontalWriter.int64Fields["int64field1"] = append(horizontalWriter.int64Fields["int64field1"], math.MaxInt32+3, math.MaxInt32+4)

	horizontalWriter.int64Fields["int64field1^max"] = append(horizontalWriter.int64Fields["int64field1^max"], math.MaxInt32+4)

	horizontalWriter.valueElementSize = 2

	horizontalWriter.txnPartition = store.GetPartition([]byte(datastore.GetKey(tick, "dummy.int64.column", 0)), tokenizer)

	err = horizontalWriter.writeFields(store, 0, horizontalWriter.batchSize, 0)

	copy(horizontalWriter.txnBufferBytes[horizontalWriter.txnOffset:], utils.EOTBytes)

	horizontalWriter.txnOffset += len(utils.EOTBytes)

	WriteINT32Value(int32(horizontalWriter.txnOffset-4), 0, horizontalWriter.txnBufferBytes)

	err = horizontalWriter.commitTxn(store, false, horizontalWriter.txnBufferBytes[:horizontalWriter.txnOffset], horizontalWriter.txnEntries, horizontalWriter.txnPartition)

	assertions.Nil(err)

	horizontalWriter.cleanupTxn()

	bytes := []byte(INT32ToStringValue(horizontalWriter.tick) + utils.KeySeparator + utils.BatchSize + datastore.DefaultKeyPart)

	bufferIndex, bufferBytes := encoder.WriteINT24Value(2, utils.MaxValueBytes)

	err = store.Put(bytes, bufferBytes, encoder, mockMetricTokenizer)

	encoder.MemoryPool.ReleaseBytePool(bufferIndex)

	assertions.Nil(err)

	horizontalWriter.clearContext()

	horizontalWriter.valueElementSize = 2

	horizontalWriter.int64Fields["int64field1"] = append(horizontalWriter.int64Fields["int64field1"], math.MaxInt32+7, math.MaxInt32+8)

	horizontalWriter.int64Fields["int64field1^max"] = append(horizontalWriter.int64Fields["int64field1^max"], math.MaxInt32+8)

	horizontalWriter.int64Fields["int64field2"] = append(horizontalWriter.int64Fields["int64field2"], math.MaxInt32+7, math.MaxInt32+8)

	horizontalWriter.int64Fields["int64field2^max"] = append(horizontalWriter.int64Fields["int64field2^max"], math.MaxInt32+8)

	horizontalWriter.txnPartition = store.GetPartition([]byte(datastore.GetKey(tick, "dummy.int64.column", 0)), tokenizer)

	err = horizontalWriter.writeFields(store, 0, horizontalWriter.batchSize, 0)

	copy(horizontalWriter.txnBufferBytes[horizontalWriter.txnOffset:], utils.EOTBytes)

	horizontalWriter.txnOffset += len(utils.EOTBytes)

	WriteINT32Value(int32(horizontalWriter.txnOffset-4), 0, horizontalWriter.txnBufferBytes)

	err = horizontalWriter.commitTxn(store, false, horizontalWriter.txnBufferBytes[:horizontalWriter.txnOffset], horizontalWriter.txnEntries, horizontalWriter.txnPartition)

	assertions.Nil(err)

	horizontalWriter.cleanupTxn()

	found, valueBytes, err := store.Get([]byte(datastore.GetKey(horizontalWriter.tick, "int64field1", 0)), make([]byte, utils.MaxValueBufferBytes), encoder, horizontalWriter.event, horizontalWriter.waitGroup, mockMetricTokenizer, true)

	assertions.True(found)

	assertions.Nil(err)

	assertions.NotNil(valueBytes)

	poolIndex, values, err := decoder.DecodeINT64Values(GetEncoding(valueBytes[0]), GetDataType(valueBytes[0]), valueBytes[1:], "", "", 0)

	assertions.EqualValues([]int64{math.MaxInt32 + 1, math.MaxInt32 + 2, math.MaxInt32 + 7, math.MaxInt32 + 8}, values)

	decoder.MemoryPool.ReleaseINT64Pool(poolIndex)

}

// Bug ID :- 24634
func TestWriteStringMetricMaxValueBufferBytesType1(t *testing.T) {

	store := datastore.GetStore("max-value-buffer-bytes-type1", utils.StaticMetric, true, true, encoder, mockMetricTokenizer)

	verticalWriter.plugin = "max-value-buffer-bytes-type1"

	err := verticalWriter.flushStringMetric("key", string(make([]byte, utils.MaxValueBufferBytes+100)), "", "")

	time.Sleep(time.Second * 1)

	assertions := assert.New(t)

	assertions.Nil(err)

	found, valueBytes, err := store.Get([]byte("key"+datastore.DefaultKeyPart), make([]byte, utils.MaxValueBufferBytes), encoder, verticalWriter.event, &verticalWriter.waitGroup, mockMetricTokenizer, true)

	assertions.True(found)

	assertions.Nil(err)

	assertions.NotNil(valueBytes)

	assertions.EqualValues(make([]byte, utils.MaxValueBufferBytes-utils.MaxValueBytes), valueBytes)
}

// // Bug ID :- 24634
func TestWriteStringMetricMaxValueBufferBytesType2(t *testing.T) {

	store := datastore.GetStore("max-value-buffer-bytes-type2", utils.StaticMetric, true, true, encoder, mockMetricTokenizer)

	verticalWriter.plugin = "max-value-buffer-bytes-type2"

	err := verticalWriter.flushStringMetric("key", string(make([]byte, utils.MaxValueBufferBytes-utils.MaxValueBytes+1)), "", "")

	time.Sleep(time.Second * 1)

	assertions := assert.New(t)

	assertions.Nil(err)

	found, valueBytes, err := store.Get([]byte("key"+datastore.DefaultKeyPart), make([]byte, utils.MaxValueBufferBytes), encoder, verticalWriter.event, &verticalWriter.waitGroup, mockMetricTokenizer, true)

	assertions.True(found)

	assertions.Nil(err)

	assertions.NotNil(valueBytes)

	assertions.EqualValues(make([]byte, utils.MaxValueBufferBytes-utils.MaxValueBytes), valueBytes)
}

func TestHorizontalWriterGetDataType(t *testing.T) {

	assertions := assert.New(t)

	horizontalWriter.clearContext()

	horizontalWriter.stringFields["column"] = append(horizontalWriter.stringFields["column"], "value")

	dataType := horizontalWriter.getDataType(datastore.IntegerColumn, "column")

	assertions.Equal(byte(datastore.StringColumn), dataType)

	horizontalWriter.clearContext()

	horizontalWriter.int32Fields["column"] = append(horizontalWriter.int32Fields["column"], 1)

	dataType = horizontalWriter.getDataType(datastore.StringColumn, "column")

	assertions.Equal(byte(datastore.StringColumn), dataType)

	assertions.Equal([]string{"1"}, horizontalWriter.stringFields["column"])

	horizontalWriter.clearContext()

	horizontalWriter.int64Fields["column"] = append(horizontalWriter.int64Fields["column"], 1)

	dataType = horizontalWriter.getDataType(datastore.IntegerColumn, "column")

	assertions.Equal(byte(datastore.IntegerColumn), dataType)

	dataType = horizontalWriter.getDataType(datastore.StringColumn, "column")

	assertions.Equal(byte(datastore.StringColumn), dataType)

	assertions.Equal([]string{"1"}, horizontalWriter.stringFields["column"])

	dataType = horizontalWriter.getDataType(datastore.StringColumn, "column")

	assertions.Equal(byte(datastore.StringColumn), dataType)

}

//Writer blob columns

func TestHorizontalBlobColumnType1(t *testing.T) {

	assertions := assert.New(t)

	column := "max.blob.column"

	utils.MaxBlobBytes = 100

	columns := []string{column}

	values := []interface{}{utils.GenerateRandomString(1000)}

	dataTypes := []byte{datastore.StringColumn}

	rowBuffer := &bytes2.Buffer{}

	for index := range columns {

		PackRowBuffer(columns[index], ToString(values[index]), dataTypes[index], rowBuffer)

	}

	batch := PackEventBatchV3("dummy.plugin.1", time.Now().Unix(), "************", utils.Log, rowBuffer)

	atomic.AddInt32(&horizontalWriter.Pending, 1)

	horizontalWriter.Events <- batch

	time.Sleep(time.Second * 3)

	assertions.True(datastore.IsBlobColumn(column))

}

func TestHorizontalBlobColumnType2(t *testing.T) {

	assertions := assert.New(t)

	column := "max.blob.column1"

	utils.MaxBlobBytes = 100

	store := datastore.GetStore("dummystore", utils.Log, true, true, encoder, horizontalWriter.tokenizer)

	horizontalWriter.dataStoreType = utils.Log

	horizontalWriter.batchSize = 1

	horizontalWriter.probes[column] = 100

	poolIndex, bytes, err, found := horizontalWriter.writeStringFieldValues([]string{utils.GenerateRandomString(100)}, column, store, 0)

	defer horizontalWriter.encoder.MemoryPool.ReleaseBytePool(poolIndex)

	assertions.True(found)

	assertions.NotNil(bytes)

	assertions.Nil(err)

	assertions.True(datastore.IsBlobColumn(column))

}

//horizontal writer

func TestHorizontalWriterHandleStringError(t *testing.T) {

	assertions := assert.New(t)

	poolIndex, _ := horizontalWriter.encoder.MemoryPool.AcquireBlobPool(utils.NotAvailable)

	bytePoolIndex, bufferBytes, err, blobColumn := horizontalWriter.handleStringError(poolIndex, errors.New(utils.ErrorGetOrdinal), []byte("key1"), "dummy-store")

	assertions.Equal(bytePoolIndex, -1)

	assertions.Nil(bufferBytes)

	assertions.NotNil(err)

	assertions.False(blobColumn)

}

func TestHorizontalWriterHandleNumericError(t *testing.T) {

	assertions := assert.New(t)

	poolIndex, _ := horizontalWriter.encoder.MemoryPool.AcquireBlobPool(utils.NotAvailable)

	bytePoolIndex, bufferBytes, err := horizontalWriter.handleNumericError(poolIndex, errors.New(utils.ErrorGetOrdinal), []byte("key1"), "dummy-store")

	assertions.Equal(bytePoolIndex, -1)

	assertions.Nil(bufferBytes)

	assertions.NotNil(err)

}

//Writer flush new testcases

/*------------------------Flush------------------------------------------------*/

func TestFlushINT8Values(t *testing.T) {

	assertions := assert.New(t)

	store := datastore.GetStore("21082022-test-flush-int8-values", utils.PerformanceMetric, true, true, verticalWriter.encoder, verticalWriter.tokenizers[0])

	expectedTicks := []int32{241028436, 241028441, 241028446, 241028451, 241028456, 241028461, 241028466, 241028471, 241028476, 241028481, 241028486, 241028491, 241028496, 241028501, 241028506}

	//key := "1^int8.metric^21082022-test-flush-int8-values"

	metricKey := "1^int8.metric"

	expectedValues := []int8{1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15}

	cacheValues := make([][]interface{}, 2)

	cacheValues[1] = []interface{}{Int8}

	for i := 1; i < 11; i++ { //max rle encoding values

		cacheValues[0] = append(cacheValues[0], expectedTicks[i])

		cacheValues[1] = append(cacheValues[1], int64(expectedValues[i]))
	}

	err := verticalWriter.flush(metricKey, store, cacheValues)

	assertions.Nil(err)

	cacheValues = make([][]interface{}, 2)

	cacheValues[1] = append(cacheValues[1], Int8)

	cacheValues[0] = append(cacheValues[0], expectedTicks[0])

	cacheValues[1] = append(cacheValues[1], int64(expectedValues[0]))

	for i := 11; i < len(expectedTicks); i++ { //max rle encoding values

		cacheValues[0] = append(cacheValues[0], expectedTicks[i])

		cacheValues[1] = append(cacheValues[1], int64(expectedValues[i]))
	}

	err = verticalWriter.flush(metricKey, store, cacheValues)

	assertions.Nil(err)

	part := UINT16ToStringValue(store.GetMaxPart(utils.GetHash64([]byte(metricKey))))

	found, valueBytes, err := store.Get([]byte(metricKey+utils.KeySeparator+part), verticalWriter.readBuffers[0], verticalWriter.encoder, verticalWriter.event, &verticalWriter.waitGroup, mockMetricTokenizer, true)

	assertions.True(found)

	assertions.Nil(err)

	assertions.NotNil(valueBytes)

	poolIndex, _, values, _, _, _, _, _, err := datastore.GetColumns(GetDataType(valueBytes[0]), valueBytes[0], valueBytes[1:], "", "", 0, decoder)

	defer decoder.MemoryPool.ReleaseINT8Pool(poolIndex)

	assertions.Nil(err)

	assertions.NotNil(values)

	assertions.EqualValues(expectedValues, values)

	found, valueBytes, err = store.Get([]byte(metricKey+utils.KeySeparator+"time"+utils.KeySeparator+part), verticalWriter.readBuffers[1], verticalWriter.encoder, verticalWriter.event, &verticalWriter.waitGroup, mockMetricTokenizer, true)

	tickIndex, ticks, err := datastore.GetTimeTicks(valueBytes, 0, decoder)

	defer decoder.MemoryPool.ReleaseINT32Pool(tickIndex)

	assertions.NotNil(ticks)

	assertions.EqualValues(expectedTicks, ticks)

	assertions.Nil(err)

}

func TestFlushINT16Values(t *testing.T) {

	assertions := assert.New(t)

	store := datastore.GetStore("21082022-test-flush-int16-values", utils.PerformanceMetric, true, true, verticalWriter.encoder, verticalWriter.tokenizers[0])

	expectedTicks := []int32{241028436, 241028441, 241028446, 241028451, 241028456, 241028461, 241028466, 241028471, 241028476, 241028481, 241028486, 241028491, 241028496, 241028501, 241028506}

	metricKey := "1^int16.metric"

	expectedValues := []int16{1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15}

	cacheValues := make([][]interface{}, 2)

	cacheValues[1] = []interface{}{Int16}

	for i := 1; i < 11; i++ { //max rle encoding values

		cacheValues[0] = append(cacheValues[0], expectedTicks[i])

		cacheValues[1] = append(cacheValues[1], int64(expectedValues[i]))
	}

	err := verticalWriter.flush(metricKey, store, cacheValues)

	assertions.Nil(err)

	cacheValues = make([][]interface{}, 2)

	cacheValues[1] = append(cacheValues[1], Int16)

	cacheValues[0] = append(cacheValues[0], expectedTicks[0])

	cacheValues[1] = append(cacheValues[1], int64(expectedValues[0]))

	for i := 11; i < len(expectedTicks); i++ { //max rle encoding values

		cacheValues[0] = append(cacheValues[0], expectedTicks[i])

		cacheValues[1] = append(cacheValues[1], int64(expectedValues[i]))
	}

	err = verticalWriter.flush(metricKey, store, cacheValues)

	assertions.Nil(err)

	part := UINT16ToStringValue(store.GetMaxPart(utils.GetHash64([]byte(metricKey))))

	found, valueBytes, err := store.Get([]byte(metricKey+utils.KeySeparator+part), verticalWriter.readBuffers[0], verticalWriter.encoder, verticalWriter.event, &verticalWriter.waitGroup, mockMetricTokenizer, true)

	assertions.True(found)

	assertions.Nil(err)

	assertions.NotNil(valueBytes)

	poolIndex, _, _, values, _, _, _, _, err := datastore.GetColumns(GetDataType(valueBytes[0]), valueBytes[0], valueBytes[1:], "", "", 0, decoder)

	defer decoder.MemoryPool.ReleaseINT16Pool(poolIndex)

	assertions.Nil(err)

	assertions.NotNil(values)

	assertions.EqualValues(expectedValues, values)

	found, valueBytes, err = store.Get([]byte(metricKey+utils.KeySeparator+"time"+utils.KeySeparator+part), verticalWriter.readBuffers[1], verticalWriter.encoder, verticalWriter.event, &verticalWriter.waitGroup, mockMetricTokenizer, true)

	tickIndex, ticks, err := datastore.GetTimeTicks(valueBytes, 0, decoder)

	defer decoder.MemoryPool.ReleaseINT32Pool(tickIndex)

	assertions.NotNil(ticks)

	assertions.EqualValues(expectedTicks, ticks)

	assertions.Nil(err)

}

func TestFlushINT24Values(t *testing.T) {

	assertions := assert.New(t)

	store := datastore.GetStore("21082022-test-flush-int24-values", utils.PerformanceMetric, true, true, verticalWriter.encoder, verticalWriter.tokenizers[0])

	expectedTicks := []int32{241028436, 241028441, 241028446, 241028451, 241028456, 241028461, 241028466, 241028471, 241028476, 241028481, 241028486, 241028491, 241028496, 241028501, 241028506}

	metricKey := "1^int24.metric"

	expectedValues := []int32{1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15}

	cacheValues := make([][]interface{}, 2)

	cacheValues[1] = []interface{}{Int24}

	for i := 1; i < 11; i++ { //max rle encoding values

		cacheValues[0] = append(cacheValues[0], expectedTicks[i])

		cacheValues[1] = append(cacheValues[1], int64(expectedValues[i]))
	}

	err := verticalWriter.flush(metricKey, store, cacheValues)

	assertions.Nil(err)

	cacheValues = make([][]interface{}, 2)

	cacheValues[1] = append(cacheValues[1], Int24)

	cacheValues[0] = append(cacheValues[0], expectedTicks[0])

	cacheValues[1] = append(cacheValues[1], int64(expectedValues[0]))

	for i := 11; i < len(expectedTicks); i++ { //max rle encoding values

		cacheValues[0] = append(cacheValues[0], expectedTicks[i])

		cacheValues[1] = append(cacheValues[1], int64(expectedValues[i]))
	}

	err = verticalWriter.flush(metricKey, store, cacheValues)

	assertions.Nil(err)

	part := UINT16ToStringValue(store.GetMaxPart(utils.GetHash64([]byte(metricKey))))

	found, valueBytes, err := store.Get([]byte(metricKey+utils.KeySeparator+part), verticalWriter.readBuffers[0], verticalWriter.encoder, verticalWriter.event, &verticalWriter.waitGroup, mockMetricTokenizer, true)

	assertions.True(found)

	assertions.Nil(err)

	assertions.NotNil(valueBytes)

	poolIndex, _, _, _, values, _, _, _, err := datastore.GetColumns(GetDataType(valueBytes[0]), valueBytes[0], valueBytes[1:], "", "", 0, decoder)

	defer decoder.MemoryPool.ReleaseINT32Pool(poolIndex)

	assertions.Nil(err)

	assertions.NotNil(values)

	assertions.EqualValues(expectedValues, values)

	found, valueBytes, err = store.Get([]byte(metricKey+utils.KeySeparator+"time"+utils.KeySeparator+part), verticalWriter.readBuffers[1], verticalWriter.encoder, verticalWriter.event, &verticalWriter.waitGroup, mockMetricTokenizer, true)

	tickIndex, ticks, err := datastore.GetTimeTicks(valueBytes, 0, decoder)

	defer decoder.MemoryPool.ReleaseINT32Pool(tickIndex)

	assertions.NotNil(ticks)

	assertions.EqualValues(expectedTicks, ticks)

	assertions.Nil(err)

}

func TestFlushINT32Values(t *testing.T) {

	assertions := assert.New(t)

	store := datastore.GetStore("21082022-test-flush-int32-values", utils.PerformanceMetric, true, true, verticalWriter.encoder, verticalWriter.tokenizers[0])

	expectedTicks := []int32{241028436, 241028441, 241028446, 241028451, 241028456, 241028461, 241028466, 241028471, 241028476, 241028481, 241028486, 241028491, 241028496, 241028501, 241028506}

	metricKey := "1^int32.metric"

	expectedValues := []int32{1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15}

	cacheValues := make([][]interface{}, 2)

	cacheValues[1] = []interface{}{Int32}

	for i := 1; i < 11; i++ { //max rle encoding values

		cacheValues[0] = append(cacheValues[0], expectedTicks[i])

		cacheValues[1] = append(cacheValues[1], int64(expectedValues[i]))
	}

	err := verticalWriter.flush(metricKey, store, cacheValues)

	assertions.Nil(err)

	cacheValues = make([][]interface{}, 2)

	cacheValues[1] = append(cacheValues[1], Int32)

	cacheValues[0] = append(cacheValues[0], expectedTicks[0])

	cacheValues[1] = append(cacheValues[1], int64(expectedValues[0]))

	for i := 11; i < len(expectedTicks); i++ { //max rle encoding values

		cacheValues[0] = append(cacheValues[0], expectedTicks[i])

		cacheValues[1] = append(cacheValues[1], int64(expectedValues[i]))
	}

	err = verticalWriter.flush(metricKey, store, cacheValues)

	assertions.Nil(err)

	part := UINT16ToStringValue(store.GetMaxPart(utils.GetHash64([]byte(metricKey))))

	found, valueBytes, err := store.Get([]byte(metricKey+utils.KeySeparator+part), verticalWriter.readBuffers[0], verticalWriter.encoder, verticalWriter.event, &verticalWriter.waitGroup, mockMetricTokenizer, true)

	assertions.True(found)

	assertions.Nil(err)

	assertions.NotNil(valueBytes)

	poolIndex, _, _, _, values, _, _, _, err := datastore.GetColumns(GetDataType(valueBytes[0]), valueBytes[0], valueBytes[1:], "", "", 0, decoder)

	defer decoder.MemoryPool.ReleaseINT32Pool(poolIndex)

	assertions.Nil(err)

	assertions.NotNil(values)

	assertions.EqualValues(expectedValues, values)

	found, valueBytes, err = store.Get([]byte(metricKey+utils.KeySeparator+"time"+utils.KeySeparator+part), verticalWriter.readBuffers[1], verticalWriter.encoder, verticalWriter.event, &verticalWriter.waitGroup, mockMetricTokenizer, true)

	tickIndex, ticks, err := datastore.GetTimeTicks(valueBytes, 0, decoder)

	defer decoder.MemoryPool.ReleaseINT32Pool(tickIndex)

	assertions.NotNil(ticks)

	assertions.EqualValues(expectedTicks, ticks)

	assertions.Nil(err)

}

func TestFlushINT40Values(t *testing.T) {

	assertions := assert.New(t)

	store := datastore.GetStore("21082022-test-flush-int40-values", utils.PerformanceMetric, true, true, verticalWriter.encoder, verticalWriter.tokenizers[0])

	expectedTicks := []int32{241028436, 241028441, 241028446, 241028451, 241028456, 241028461, 241028466, 241028471, 241028476, 241028481, 241028486, 241028491, 241028496, 241028501, 241028506}

	metricKey := "1^int40.metric"

	expectedValues := []int64{1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15}

	cacheValues := make([][]interface{}, 2)

	cacheValues[1] = []interface{}{Int40}

	for i := 1; i < 11; i++ { //max rle encoding values

		cacheValues[0] = append(cacheValues[0], expectedTicks[i])

		cacheValues[1] = append(cacheValues[1], expectedValues[i])
	}

	err := verticalWriter.flush(metricKey, store, cacheValues)

	assertions.Nil(err)

	cacheValues = make([][]interface{}, 2)

	cacheValues[1] = append(cacheValues[1], Int40)

	cacheValues[0] = append(cacheValues[0], expectedTicks[0])

	cacheValues[1] = append(cacheValues[1], expectedValues[0])

	for i := 11; i < len(expectedTicks); i++ { //max rle encoding values

		cacheValues[0] = append(cacheValues[0], expectedTicks[i])

		cacheValues[1] = append(cacheValues[1], expectedValues[i])
	}

	err = verticalWriter.flush(metricKey, store, cacheValues)

	assertions.Nil(err)

	part := UINT16ToStringValue(store.GetMaxPart(utils.GetHash64([]byte(metricKey))))

	found, valueBytes, err := store.Get([]byte(metricKey+utils.KeySeparator+part), verticalWriter.readBuffers[0], verticalWriter.encoder, verticalWriter.event, &verticalWriter.waitGroup, mockMetricTokenizer, true)

	assertions.True(found)

	assertions.Nil(err)

	assertions.NotNil(valueBytes)

	poolIndex, _, _, _, _, values, _, _, err := datastore.GetColumns(GetDataType(valueBytes[0]), valueBytes[0], valueBytes[1:], "", "", 0, decoder)

	defer decoder.MemoryPool.ReleaseINT64Pool(poolIndex)

	assertions.Nil(err)

	assertions.NotNil(values)

	assertions.EqualValues(expectedValues, values)

	found, valueBytes, err = store.Get([]byte(metricKey+utils.KeySeparator+"time"+utils.KeySeparator+part), verticalWriter.readBuffers[1], verticalWriter.encoder, verticalWriter.event, &verticalWriter.waitGroup, mockMetricTokenizer, true)

	tickIndex, ticks, err := datastore.GetTimeTicks(valueBytes, 0, decoder)

	defer decoder.MemoryPool.ReleaseINT32Pool(tickIndex)

	assertions.NotNil(ticks)

	assertions.EqualValues(expectedTicks, ticks)

	assertions.Nil(err)

}

func TestFlushINT48Values(t *testing.T) {

	assertions := assert.New(t)

	store := datastore.GetStore("21082022-test-flush-int48-values", utils.PerformanceMetric, true, true, verticalWriter.encoder, verticalWriter.tokenizers[0])

	expectedTicks := []int32{241028436, 241028441, 241028446, 241028451, 241028456, 241028461, 241028466, 241028471, 241028476, 241028481, 241028486, 241028491, 241028496, 241028501, 241028506}

	metricKey := "1^int48.metric"

	expectedValues := []int64{1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15}

	cacheValues := make([][]interface{}, 2)

	cacheValues[1] = []interface{}{Int48}

	for i := 1; i < 11; i++ { //max rle encoding values

		cacheValues[0] = append(cacheValues[0], expectedTicks[i])

		cacheValues[1] = append(cacheValues[1], expectedValues[i])
	}

	err := verticalWriter.flush(metricKey, store, cacheValues)

	assertions.Nil(err)

	cacheValues = make([][]interface{}, 2)

	cacheValues[1] = append(cacheValues[1], Int48)

	cacheValues[0] = append(cacheValues[0], expectedTicks[0])

	cacheValues[1] = append(cacheValues[1], expectedValues[0])

	for i := 11; i < len(expectedTicks); i++ { //max rle encoding values

		cacheValues[0] = append(cacheValues[0], expectedTicks[i])

		cacheValues[1] = append(cacheValues[1], expectedValues[i])
	}

	err = verticalWriter.flush(metricKey, store, cacheValues)

	assertions.Nil(err)

	part := UINT16ToStringValue(store.GetMaxPart(utils.GetHash64([]byte(metricKey))))

	found, valueBytes, err := store.Get([]byte(metricKey+utils.KeySeparator+part), verticalWriter.readBuffers[0], verticalWriter.encoder, verticalWriter.event, &verticalWriter.waitGroup, mockMetricTokenizer, true)

	assertions.True(found)

	assertions.Nil(err)

	assertions.NotNil(valueBytes)

	poolIndex, _, _, _, _, values, _, _, err := datastore.GetColumns(GetDataType(valueBytes[0]), valueBytes[0], valueBytes[1:], "", "", 0, decoder)

	defer decoder.MemoryPool.ReleaseINT64Pool(poolIndex)

	assertions.Nil(err)

	assertions.NotNil(values)

	assertions.EqualValues(expectedValues, values)

	found, valueBytes, err = store.Get([]byte(metricKey+utils.KeySeparator+"time"+utils.KeySeparator+part), verticalWriter.readBuffers[1], verticalWriter.encoder, verticalWriter.event, &verticalWriter.waitGroup, mockMetricTokenizer, true)

	tickIndex, ticks, err := datastore.GetTimeTicks(valueBytes, 0, decoder)

	defer decoder.MemoryPool.ReleaseINT32Pool(tickIndex)

	assertions.NotNil(ticks)

	assertions.EqualValues(expectedTicks, ticks)

	assertions.Nil(err)

}

func TestFlushINT56Values(t *testing.T) {

	assertions := assert.New(t)

	store := datastore.GetStore("21082022-test-flush-int56-values", utils.PerformanceMetric, true, true, verticalWriter.encoder, verticalWriter.tokenizers[0])

	expectedTicks := []int32{241028436, 241028441, 241028446, 241028451, 241028456, 241028461, 241028466, 241028471, 241028476, 241028481, 241028486, 241028491, 241028496, 241028501, 241028506}

	metricKey := "1^int56.metric"

	expectedValues := []int64{1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15}

	cacheValues := make([][]interface{}, 2)

	cacheValues[1] = []interface{}{Int56}

	for i := 1; i < 11; i++ { //max rle encoding values

		cacheValues[0] = append(cacheValues[0], expectedTicks[i])

		cacheValues[1] = append(cacheValues[1], expectedValues[i])
	}

	err := verticalWriter.flush(metricKey, store, cacheValues)

	assertions.Nil(err)

	cacheValues = make([][]interface{}, 2)

	cacheValues[1] = append(cacheValues[1], Int56)

	cacheValues[0] = append(cacheValues[0], expectedTicks[0])

	cacheValues[1] = append(cacheValues[1], expectedValues[0])

	for i := 11; i < len(expectedTicks); i++ { //max rle encoding values

		cacheValues[0] = append(cacheValues[0], expectedTicks[i])

		cacheValues[1] = append(cacheValues[1], expectedValues[i])
	}

	err = verticalWriter.flush(metricKey, store, cacheValues)

	assertions.Nil(err)

	part := UINT16ToStringValue(store.GetMaxPart(utils.GetHash64([]byte(metricKey))))

	found, valueBytes, err := store.Get([]byte(metricKey+utils.KeySeparator+part), verticalWriter.readBuffers[0], verticalWriter.encoder, verticalWriter.event, &verticalWriter.waitGroup, mockMetricTokenizer, true)

	assertions.True(found)

	assertions.Nil(err)

	assertions.NotNil(valueBytes)

	poolIndex, _, _, _, _, values, _, _, err := datastore.GetColumns(GetDataType(valueBytes[0]), valueBytes[0], valueBytes[1:], "", "", 0, decoder)

	defer decoder.MemoryPool.ReleaseINT64Pool(poolIndex)

	assertions.Nil(err)

	assertions.NotNil(values)

	assertions.EqualValues(expectedValues, values)

	found, valueBytes, err = store.Get([]byte(metricKey+utils.KeySeparator+"time"+utils.KeySeparator+part), verticalWriter.readBuffers[1], verticalWriter.encoder, verticalWriter.event, &verticalWriter.waitGroup, mockMetricTokenizer, true)

	tickIndex, ticks, err := datastore.GetTimeTicks(valueBytes, 0, decoder)

	defer decoder.MemoryPool.ReleaseINT32Pool(tickIndex)

	assertions.NotNil(ticks)

	assertions.EqualValues(expectedTicks, ticks)

	assertions.Nil(err)

}

func TestFlushINT64Values(t *testing.T) {

	assertions := assert.New(t)

	store := datastore.GetStore("21082022-test-flush-int64-values", utils.PerformanceMetric, true, true, verticalWriter.encoder, verticalWriter.tokenizers[0])

	expectedTicks := []int32{241028436, 241028441, 241028446, 241028451, 241028456, 241028461, 241028466, 241028471, 241028476, 241028481, 241028486, 241028491, 241028496, 241028501, 241028506}

	metricKey := "1^int64.metric"

	expectedValues := []int64{1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15}

	cacheValues := make([][]interface{}, 2)

	cacheValues[1] = []interface{}{Int64}

	for i := 1; i < 11; i++ { //max rle encoding values

		cacheValues[0] = append(cacheValues[0], expectedTicks[i])

		cacheValues[1] = append(cacheValues[1], expectedValues[i])
	}

	err := verticalWriter.flush(metricKey, store, cacheValues)

	assertions.Nil(err)

	cacheValues = make([][]interface{}, 2)

	cacheValues[1] = append(cacheValues[1], Int64)

	cacheValues[0] = append(cacheValues[0], expectedTicks[0])

	cacheValues[1] = append(cacheValues[1], expectedValues[0])

	for i := 11; i < len(expectedTicks); i++ { //max rle encoding values

		cacheValues[0] = append(cacheValues[0], expectedTicks[i])

		cacheValues[1] = append(cacheValues[1], expectedValues[i])
	}

	err = verticalWriter.flush(metricKey, store, cacheValues)

	assertions.Nil(err)

	part := UINT16ToStringValue(store.GetMaxPart(utils.GetHash64([]byte(metricKey))))

	found, valueBytes, err := store.Get([]byte(metricKey+utils.KeySeparator+part), verticalWriter.readBuffers[0], verticalWriter.encoder, verticalWriter.event, &verticalWriter.waitGroup, mockMetricTokenizer, true)

	assertions.True(found)

	assertions.Nil(err)

	assertions.NotNil(valueBytes)

	poolIndex, _, _, _, _, values, _, _, err := datastore.GetColumns(GetDataType(valueBytes[0]), valueBytes[0], valueBytes[1:], "", "", 0, decoder)

	defer decoder.MemoryPool.ReleaseINT64Pool(poolIndex)

	assertions.Nil(err)

	assertions.NotNil(values)

	assertions.EqualValues(expectedValues, values)

	found, valueBytes, err = store.Get([]byte(metricKey+utils.KeySeparator+"time"+utils.KeySeparator+part), verticalWriter.readBuffers[1], verticalWriter.encoder, verticalWriter.event, &verticalWriter.waitGroup, mockMetricTokenizer, true)

	tickIndex, ticks, err := datastore.GetTimeTicks(valueBytes, 0, decoder)

	defer decoder.MemoryPool.ReleaseINT32Pool(tickIndex)

	assertions.NotNil(ticks)

	assertions.EqualValues(expectedTicks, ticks)

	assertions.Nil(err)

}

func TestFlushFLOAT8Values(t *testing.T) {

	assertions := assert.New(t)

	store := datastore.GetStore("21082022-test-flush-float8-values", utils.PerformanceMetric, true, true, verticalWriter.encoder, verticalWriter.tokenizers[0])

	expectedTicks := []int32{241028436, 241028441, 241028446, 241028451, 241028456, 241028461, 241028466, 241028471, 241028476, 241028481, 241028486, 241028491, 241028496, 241028501, 241028506}

	metricKey := "1^float8.metric"

	expectedValues := []float64{1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15}

	cacheValues := make([][]interface{}, 2)

	cacheValues[1] = []interface{}{Float8}

	for i := 1; i < 11; i++ { //max rle encoding values

		cacheValues[0] = append(cacheValues[0], expectedTicks[i])

		cacheValues[1] = append(cacheValues[1], expectedValues[i])
	}

	err := verticalWriter.flush(metricKey, store, cacheValues)

	assertions.Nil(err)

	cacheValues = make([][]interface{}, 2)

	cacheValues[1] = append(cacheValues[1], Float8)

	cacheValues[0] = append(cacheValues[0], expectedTicks[0])

	cacheValues[1] = append(cacheValues[1], expectedValues[0])

	for i := 11; i < len(expectedTicks); i++ { //max rle encoding values

		cacheValues[0] = append(cacheValues[0], expectedTicks[i])

		cacheValues[1] = append(cacheValues[1], expectedValues[i])
	}

	err = verticalWriter.flush(metricKey, store, cacheValues)

	assertions.Nil(err)

	part := UINT16ToStringValue(store.GetMaxPart(utils.GetHash64([]byte(metricKey))))

	found, valueBytes, err := store.Get([]byte(metricKey+utils.KeySeparator+part), verticalWriter.readBuffers[0], verticalWriter.encoder, verticalWriter.event, &verticalWriter.waitGroup, mockMetricTokenizer, true)

	assertions.True(found)

	assertions.Nil(err)

	assertions.NotNil(valueBytes)

	poolIndex, _, _, _, _, _, values, _, err := datastore.GetColumns(GetDataType(valueBytes[0]), valueBytes[0], valueBytes[1:], "", "", 0, decoder)

	defer decoder.MemoryPool.ReleaseFLOAT64Pool(poolIndex)

	assertions.Nil(err)

	assertions.NotNil(values)

	assertions.EqualValues(expectedValues, values)

	found, valueBytes, err = store.Get([]byte(metricKey+utils.KeySeparator+"time"+utils.KeySeparator+part), verticalWriter.readBuffers[1], verticalWriter.encoder, verticalWriter.event, &verticalWriter.waitGroup, mockMetricTokenizer, true)

	tickIndex, ticks, err := datastore.GetTimeTicks(valueBytes, 0, decoder)

	defer decoder.MemoryPool.ReleaseINT32Pool(tickIndex)

	assertions.NotNil(ticks)

	assertions.EqualValues(expectedTicks, ticks)

	assertions.Nil(err)

}

func TestFlushFLOAT16Values(t *testing.T) {

	assertions := assert.New(t)

	store := datastore.GetStore("21082022-test-flush-float16-values", utils.PerformanceMetric, true, true, encoder, mockMetricTokenizer)

	expectedTicks := []int32{241028436, 241028441, 241028446, 241028451, 241028456, 241028461, 241028466, 241028471, 241028476, 241028481, 241028486, 241028491, 241028496, 241028501, 241028506}

	metricKey := "1^float16.metric"

	expectedValues := []float64{1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15}

	cacheValues := make([][]interface{}, 2)

	cacheValues[1] = []interface{}{Float16}

	for i := 1; i < 11; i++ { //max rle encoding values

		cacheValues[0] = append(cacheValues[0], expectedTicks[i])

		cacheValues[1] = append(cacheValues[1], expectedValues[i])
	}

	err := verticalWriter.flush(metricKey, store, cacheValues)

	assertions.Nil(err)

	cacheValues = make([][]interface{}, 2)

	cacheValues[1] = append(cacheValues[1], Float16)

	cacheValues[0] = append(cacheValues[0], expectedTicks[0])

	cacheValues[1] = append(cacheValues[1], expectedValues[0])

	for i := 11; i < len(expectedTicks); i++ { //max rle encoding values

		cacheValues[0] = append(cacheValues[0], expectedTicks[i])

		cacheValues[1] = append(cacheValues[1], expectedValues[i])
	}

	err = verticalWriter.flush(metricKey, store, cacheValues)

	assertions.Nil(err)

	part := UINT16ToStringValue(store.GetMaxPart(utils.GetHash64([]byte(metricKey))))

	found, valueBytes, err := store.Get([]byte(metricKey+utils.KeySeparator+part), verticalWriter.readBuffers[0], verticalWriter.encoder, verticalWriter.event, &verticalWriter.waitGroup, mockMetricTokenizer, true)

	assertions.True(found)

	assertions.Nil(err)

	assertions.NotNil(valueBytes)

	poolIndex, _, _, _, _, _, values, _, err := datastore.GetColumns(GetDataType(valueBytes[0]), valueBytes[0], valueBytes[1:], "", "", 0, decoder)

	defer decoder.MemoryPool.ReleaseFLOAT64Pool(poolIndex)

	assertions.Nil(err)

	assertions.NotNil(values)

	assertions.EqualValues(expectedValues, values)

	found, valueBytes, err = store.Get([]byte(metricKey+utils.KeySeparator+"time"+utils.KeySeparator+part), verticalWriter.readBuffers[1], verticalWriter.encoder, verticalWriter.event, &verticalWriter.waitGroup, mockMetricTokenizer, true)

	tickIndex, ticks, err := datastore.GetTimeTicks(valueBytes, 0, decoder)

	defer decoder.MemoryPool.ReleaseINT32Pool(tickIndex)

	assertions.NotNil(ticks)

	assertions.EqualValues(expectedTicks, ticks)

	assertions.Nil(err)

}

func TestFlushFLOAT64Values(t *testing.T) {

	assertions := assert.New(t)

	store := datastore.GetStore("21082022-test-flush-float64-values", utils.PerformanceMetric, true, true, encoder, mockMetricTokenizer)

	expectedTicks := []int32{241028436, 241028441, 241028446, 241028451, 241028456, 241028461, 241028466, 241028471, 241028476, 241028481, 241028486, 241028491, 241028496, 241028501, 241028506}

	metricKey := "1^float64.metric"

	expectedValues := []float64{1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15}

	cacheValues := make([][]interface{}, 2)

	cacheValues[1] = []interface{}{Float64}

	for i := 1; i < 11; i++ { //max rle encoding values

		cacheValues[0] = append(cacheValues[0], expectedTicks[i])

		cacheValues[1] = append(cacheValues[1], expectedValues[i])
	}

	err := verticalWriter.flush(metricKey, store, cacheValues)

	assertions.Nil(err)

	cacheValues = make([][]interface{}, 2)

	cacheValues[1] = append(cacheValues[1], Float64)

	cacheValues[0] = append(cacheValues[0], expectedTicks[0])

	cacheValues[1] = append(cacheValues[1], expectedValues[0])

	for i := 11; i < len(expectedTicks); i++ { //max rle encoding values

		cacheValues[0] = append(cacheValues[0], expectedTicks[i])

		cacheValues[1] = append(cacheValues[1], expectedValues[i])
	}

	err = verticalWriter.flush(metricKey, store, cacheValues)

	assertions.Nil(err)

	part := UINT16ToStringValue(store.GetMaxPart(utils.GetHash64([]byte(metricKey))))

	found, valueBytes, err := store.Get([]byte(metricKey+utils.KeySeparator+part), verticalWriter.readBuffers[0], verticalWriter.encoder, verticalWriter.event, &verticalWriter.waitGroup, mockMetricTokenizer, true)

	assertions.True(found)

	assertions.Nil(err)

	assertions.NotNil(valueBytes)

	poolIndex, _, _, _, _, _, values, _, err := datastore.GetColumns(GetDataType(valueBytes[0]), valueBytes[0], valueBytes[1:], "", "", 0, decoder)

	defer decoder.MemoryPool.ReleaseFLOAT64Pool(poolIndex)

	assertions.Nil(err)

	assertions.NotNil(values)

	assertions.EqualValues(expectedValues, values)

	found, valueBytes, err = store.Get([]byte(metricKey+utils.KeySeparator+"time"+utils.KeySeparator+part), verticalWriter.readBuffers[1], verticalWriter.encoder, verticalWriter.event, &verticalWriter.waitGroup, mockMetricTokenizer, true)

	tickIndex, ticks, err := datastore.GetTimeTicks(valueBytes, 0, decoder)

	defer decoder.MemoryPool.ReleaseINT32Pool(tickIndex)

	assertions.NotNil(ticks)

	assertions.EqualValues(expectedTicks, ticks)

	assertions.Nil(err)

}

// unsorted ticks
func TestFlushINT16ValuesType1(t *testing.T) {

	assertions := assert.New(t)

	store := datastore.GetStore("21082022-test1-flush-int16-values", utils.PerformanceMetric, true, true, verticalWriter.encoder, verticalWriter.tokenizers[0])

	expectedTicks := []int32{241028436, 241028441, 241028446, 241028451, 241028456, 241028461, 241028466, 241028471, 241028476, 241028481, 241028486, 241028491, 241028496, 241028506, 241028501}

	metricKey := "1^int16.metric"

	expectedValues := []int16{1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 15, 14}

	cacheValues := make([][]interface{}, 2)

	cacheValues[1] = []interface{}{Int16}

	for i := 1; i < 11; i++ { //max rle encoding values

		cacheValues[0] = append(cacheValues[0], expectedTicks[i])

		cacheValues[1] = append(cacheValues[1], int64(expectedValues[i]))
	}

	err := verticalWriter.flush(metricKey, store, cacheValues)

	assertions.Nil(err)

	cacheValues = make([][]interface{}, 2)

	cacheValues[1] = append(cacheValues[1], Int16)

	cacheValues[0] = append(cacheValues[0], expectedTicks[0])

	cacheValues[1] = append(cacheValues[1], int64(expectedValues[0]))

	for index := 11; index < len(expectedTicks); index++ { //max rle encoding values

		cacheValues[0] = append(cacheValues[0], expectedTicks[index])

		cacheValues[1] = append(cacheValues[1], int64(expectedValues[index]))
	}

	err = verticalWriter.flush(metricKey, store, cacheValues)

	assertions.Nil(err)

	part := UINT16ToStringValue(store.GetMaxPart(utils.GetHash64([]byte(metricKey))))

	found, valueBytes, err := store.Get([]byte(metricKey+utils.KeySeparator+part), verticalWriter.readBuffers[0], verticalWriter.encoder, verticalWriter.event, &verticalWriter.waitGroup, mockMetricTokenizer, true)

	assertions.True(found)

	assertions.Nil(err)

	assertions.NotNil(valueBytes)

	poolIndex, _, _, values, _, _, _, _, err := datastore.GetColumns(GetDataType(valueBytes[0]), valueBytes[0], valueBytes[1:], "", "", 0, decoder)

	defer decoder.MemoryPool.ReleaseINT16Pool(poolIndex)

	assertions.Nil(err)

	assertions.NotNil(values)

	expectedValues = []int16{1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15}

	assertions.EqualValues(expectedValues, values)

	found, valueBytes, err = store.Get([]byte(metricKey+utils.KeySeparator+"time"+utils.KeySeparator+part), verticalWriter.readBuffers[1], verticalWriter.encoder, verticalWriter.event, &verticalWriter.waitGroup, mockMetricTokenizer, true)

	tickIndex, ticks, err := datastore.GetTimeTicks(valueBytes, 0, decoder)

	defer decoder.MemoryPool.ReleaseINT32Pool(tickIndex)

	assertions.NotNil(ticks)

	expectedTicks = []int32{241028436, 241028441, 241028446, 241028451, 241028456, 241028461, 241028466, 241028471, 241028476, 241028481, 241028486, 241028491, 241028496, 241028501, 241028506}

	assertions.EqualValues(expectedTicks, ticks)

	assertions.Nil(err)

}

func TestFlushStringValues(t *testing.T) {

	assertion := assert.New(t)

	store := datastore.GetStore("21082022-test1-flush-string-values", utils.PerformanceMetric, true, true, verticalWriter.encoder, verticalWriter.tokenizers[0])

	expectedTicks := []int32{241028436, 241028441, 241028446, 241028451, 241028456, 241028461, 241028466, 241028471, 241028476, 241028481, 241028486, 241028491, 241028496, 241028506, 241028501}

	metricKey := "1^string.metric"

	expectedValues := []string{"metric1", "metric2", "metric3", "metric4", "metric5", "metric6", "metric7", "metric8", "metric9", "metric10", "metric11", "metric12", "metric13", "metric15", "metric14"}

	cacheValues := make([][]interface{}, 2)

	cacheValues[1] = []interface{}{String}

	for index := 1; index < 11; index++ {

		cacheValues[0] = append(cacheValues[0], expectedTicks[index])

		cacheValues[1] = append(cacheValues[1], expectedValues[index])

	}

	err := verticalWriter.flush(metricKey, store, cacheValues)

	assertion.Nil(err)

	cacheValues = make([][]interface{}, 2)

	cacheValues[1] = append(cacheValues[1], String)

	cacheValues[0] = append(cacheValues[0], expectedTicks[0])

	cacheValues[1] = append(cacheValues[1], expectedValues[0])

	for index := 11; index < len(expectedTicks); index++ {

		cacheValues[0] = append(cacheValues[0], expectedTicks[index])

		cacheValues[1] = append(cacheValues[1], expectedValues[index])

	}

	err = verticalWriter.flush(metricKey, store, cacheValues)

	assertion.Nil(err)

	part := UINT16ToStringValue(store.GetMaxPart(utils.GetHash64([]byte(metricKey))))

	found, valueBytes, err := store.Get([]byte(metricKey+utils.KeySeparator+part), verticalWriter.readBuffers[0], verticalWriter.encoder, verticalWriter.event, &verticalWriter.waitGroup, mockMetricTokenizer, true)

	assertion.True(found)

	assertion.Nil(err)

	assertion.NotNil(valueBytes)

	poolIndex, _, _, _, _, _, _, values, err := datastore.GetColumns(GetDataType(valueBytes[0]), valueBytes[0], valueBytes[1:], "", "", 0, decoder)

	defer decoder.MemoryPool.ReleaseStringPool(poolIndex)

	assertion.Nil(err)

	assertion.NotNil(values)

	expectedValues = []string{"metric1", "metric2", "metric3", "metric4", "metric5", "metric6", "metric7", "metric8", "metric9", "metric10", "metric11", "metric12", "metric13", "metric14", "metric15"}

	assertion.EqualValues(expectedValues, values)

	found, valueBytes, err = store.Get([]byte(metricKey+utils.KeySeparator+"time"+utils.KeySeparator+part), verticalWriter.readBuffers[0], verticalWriter.encoder, verticalWriter.event, &verticalWriter.waitGroup, mockMetricTokenizer, true)

	tickIndex, ticks, err := datastore.GetTimeTicks(valueBytes, 0, decoder)

	defer decoder.MemoryPool.ReleaseINT32Pool(tickIndex)

	assertion.NotNil(ticks)

	expectedTicks = []int32{241028436, 241028441, 241028446, 241028451, 241028456, 241028461, 241028466, 241028471, 241028476, 241028481, 241028486, 241028491, 241028496, 241028501, 241028506}

	assertion.EqualValues(expectedTicks, ticks)

	assertion.Nil(err)
}

func TestFlushOverflowInt16(t *testing.T) {

	poolLength := utils.MaxPoolLength

	utils.MaxPoolLength = 10

	defer func() {
		utils.MaxPoolLength = poolLength
	}()

	assertions := assert.New(t)

	store := datastore.GetStore("21082022-test2-flush-int16-values", utils.PerformanceMetric, true, true, verticalWriter.encoder, verticalWriter.tokenizers[0])

	expectedTicks := []int32{241028436, 241028441, 241028446, 241028451, 241028456, 241028461, 241028466, 241028471, 241028476, 241028481, 241028486, 241028491, 241028496, 241028506, 241028501}

	metricKey := "1^int16.metric"

	expectedValues := []int16{1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 15, 14}

	cacheValues := make([][]interface{}, 2)

	cacheValues[1] = []interface{}{Int16}

	for index := 1; index < 11; index++ { //max rle encoding values

		cacheValues[0] = append(cacheValues[0], expectedTicks[index])

		cacheValues[1] = append(cacheValues[1], int64(expectedValues[index]))
	}

	utils.MaxPoolLength = 10

	writer := NewVerticalWriter(0, []*StaticMetricWriter{staticMetricWriter})

	writer.Start()

	err := writer.flush(metricKey, store, cacheValues)

	assertions.Nil(err)

	cacheValues = make([][]interface{}, 2)

	cacheValues[1] = append(cacheValues[1], Int16)

	cacheValues[0] = append(cacheValues[0], expectedTicks[0])

	cacheValues[1] = append(cacheValues[1], int64(expectedValues[0]))

	for i := 11; i < len(expectedTicks); i++ { //max rle encoding values

		cacheValues[0] = append(cacheValues[0], expectedTicks[i])

		cacheValues[1] = append(cacheValues[1], int64(expectedValues[i]))
	}

	err = writer.flush(metricKey, store, cacheValues)

	assertions.Nil(err)

	part := UINT16ToStringValue(store.GetMaxPart(utils.GetHash64([]byte(metricKey))))

	found, valueBytes, err := store.Get([]byte(metricKey+utils.KeySeparator+part), writer.readBuffers[0], writer.encoder, writer.event, &writer.waitGroup, mockMetricTokenizer, true)

	assertions.True(found)

	assertions.Nil(err)

	assertions.NotNil(valueBytes)

	poolIndex, _, _, values, _, _, _, _, err := datastore.GetColumns(GetDataType(valueBytes[0]), valueBytes[0], valueBytes[1:], "", "", 0, decoder)

	defer decoder.MemoryPool.ReleaseINT16Pool(poolIndex)

	assertions.Nil(err)

	assertions.NotNil(values)

	expectedValues = []int16{1, 12, 13, 14, 15}

	assertions.EqualValues(expectedValues, values)

	found, valueBytes, err = store.Get([]byte(metricKey+utils.KeySeparator+"time"+utils.KeySeparator+part), writer.readBuffers[1], writer.encoder, writer.event, &writer.waitGroup, mockMetricTokenizer, true)

	tickIndex, ticks, err := datastore.GetTimeTicks(valueBytes, 0, decoder)

	defer decoder.MemoryPool.ReleaseINT32Pool(tickIndex)

	assertions.NotNil(ticks)

	expectedTicks = []int32{241028436, 241028491, 241028496, 241028501, 241028506}

	assertions.EqualValues(expectedTicks, ticks)

	assertions.Nil(err)

}

func TestFlushOverflowInt32(t *testing.T) {

	poolLength := utils.MaxPoolLength

	utils.MaxPoolLength = 10

	defer func() {
		utils.MaxPoolLength = poolLength
	}()

	assertions := assert.New(t)

	store := datastore.GetStore("21082022-test2-flush-int16-values", utils.PerformanceMetric, true, true, verticalWriter.encoder, verticalWriter.tokenizers[0])

	expectedTicks := []int32{241028436, 241028441, 241028446, 241028451, 241028456, 241028461, 241028466, 241028471, 241028476, 241028481, 241028486, 241028491, 241028496, 241028506, 241028501}

	metricKey := "1^int16.metric"

	expectedValues := []int32{1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 15, 14}

	cacheValues := make([][]interface{}, 2)

	cacheValues[1] = []interface{}{Int32}

	for index := 1; index < 11; index++ { //max rle encoding values

		cacheValues[0] = append(cacheValues[0], expectedTicks[index])

		cacheValues[1] = append(cacheValues[1], int64(expectedValues[index]))
	}

	utils.MaxPoolLength = 10

	writer := NewVerticalWriter(0, []*StaticMetricWriter{staticMetricWriter})

	writer.Start()

	err := writer.flush(metricKey, store, cacheValues)

	assertions.Nil(err)

	cacheValues = make([][]interface{}, 2)

	cacheValues[1] = append(cacheValues[1], Int32)

	cacheValues[0] = append(cacheValues[0], expectedTicks[0])

	cacheValues[1] = append(cacheValues[1], int64(expectedValues[0]))

	for i := 11; i < len(expectedTicks); i++ { //max rle encoding values

		cacheValues[0] = append(cacheValues[0], expectedTicks[i])

		cacheValues[1] = append(cacheValues[1], int64(expectedValues[i]))
	}

	err = writer.flush(metricKey, store, cacheValues)

	assertions.Nil(err)

	part := UINT16ToStringValue(store.GetMaxPart(utils.GetHash64([]byte(metricKey))))

	found, valueBytes, err := store.Get([]byte(metricKey+utils.KeySeparator+part), writer.readBuffers[0], writer.encoder, writer.event, &writer.waitGroup, mockMetricTokenizer, true)

	assertions.True(found)

	assertions.Nil(err)

	assertions.NotNil(valueBytes)

	poolIndex, _, _, _, values, _, _, _, err := datastore.GetColumns(GetDataType(valueBytes[0]), valueBytes[0], valueBytes[1:], "", "", 0, decoder)

	defer decoder.MemoryPool.ReleaseINT32Pool(poolIndex)

	assertions.Nil(err)

	assertions.NotNil(values)

	expectedValues = []int32{1, 12, 13, 14, 15}

	assertions.EqualValues(expectedValues, values)

	found, valueBytes, err = store.Get([]byte(metricKey+utils.KeySeparator+"time"+utils.KeySeparator+part), writer.readBuffers[1], writer.encoder, writer.event, &writer.waitGroup, mockMetricTokenizer, true)

	tickIndex, ticks, err := datastore.GetTimeTicks(valueBytes, 0, decoder)

	defer decoder.MemoryPool.ReleaseINT32Pool(tickIndex)

	assertions.NotNil(ticks)

	expectedTicks = []int32{241028436, 241028491, 241028496, 241028501, 241028506}

	assertions.EqualValues(expectedTicks, ticks)

	assertions.Nil(err)

}

func TestFlushOverflowInt64(t *testing.T) {

	poolLength := utils.MaxPoolLength

	utils.MaxPoolLength = 10

	defer func() {
		utils.MaxPoolLength = poolLength
	}()

	assertions := assert.New(t)

	store := datastore.GetStore("21082022-test2-flush-int16-values", utils.PerformanceMetric, true, true, verticalWriter.encoder, verticalWriter.tokenizers[0])

	expectedTicks := []int32{241028436, 241028441, 241028446, 241028451, 241028456, 241028461, 241028466, 241028471, 241028476, 241028481, 241028486, 241028491, 241028496, 241028506, 241028501}

	metricKey := "1^int16.metric"

	expectedValues := []int64{1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 15, 14}

	cacheValues := make([][]interface{}, 2)

	cacheValues[1] = []interface{}{Int64}

	for index := 1; index < 11; index++ { //max rle encoding values

		cacheValues[0] = append(cacheValues[0], expectedTicks[index])

		cacheValues[1] = append(cacheValues[1], expectedValues[index])
	}

	utils.MaxPoolLength = 10

	writer := NewVerticalWriter(0, []*StaticMetricWriter{staticMetricWriter})

	writer.Start()

	err := writer.flush(metricKey, store, cacheValues)

	assertions.Nil(err)

	cacheValues = make([][]interface{}, 2)

	cacheValues[1] = append(cacheValues[1], Int64)

	cacheValues[0] = append(cacheValues[0], expectedTicks[0])

	cacheValues[1] = append(cacheValues[1], expectedValues[0])

	for i := 11; i < len(expectedTicks); i++ { //max rle encoding values

		cacheValues[0] = append(cacheValues[0], expectedTicks[i])

		cacheValues[1] = append(cacheValues[1], expectedValues[i])
	}

	err = writer.flush(metricKey, store, cacheValues)

	assertions.Nil(err)

	part := UINT16ToStringValue(store.GetMaxPart(utils.GetHash64([]byte(metricKey))))

	found, valueBytes, err := store.Get([]byte(metricKey+utils.KeySeparator+part), writer.readBuffers[0], writer.encoder, writer.event, &writer.waitGroup, mockMetricTokenizer, true)

	assertions.True(found)

	assertions.Nil(err)

	assertions.NotNil(valueBytes)

	poolIndex, _, _, _, _, values, _, _, err := datastore.GetColumns(GetDataType(valueBytes[0]), valueBytes[0], valueBytes[1:], "", "", 0, decoder)

	defer decoder.MemoryPool.ReleaseINT64Pool(poolIndex)

	assertions.Nil(err)

	assertions.NotNil(values)

	expectedValues = []int64{1, 12, 13, 14, 15}

	assertions.EqualValues(expectedValues, values)

	found, valueBytes, err = store.Get([]byte(metricKey+utils.KeySeparator+"time"+utils.KeySeparator+part), writer.readBuffers[1], writer.encoder, writer.event, &writer.waitGroup, mockMetricTokenizer, true)

	tickIndex, ticks, err := datastore.GetTimeTicks(valueBytes, 0, decoder)

	defer decoder.MemoryPool.ReleaseINT32Pool(tickIndex)

	assertions.NotNil(ticks)

	expectedTicks = []int32{241028436, 241028491, 241028496, 241028501, 241028506}

	assertions.EqualValues(expectedTicks, ticks)

	assertions.Nil(err)

}

func TestFlushOverflowFloat(t *testing.T) {

	poolLength := utils.MaxPoolLength

	utils.MaxPoolLength = 10

	defer func() {
		utils.MaxPoolLength = poolLength
	}()

	assertions := assert.New(t)

	store := datastore.GetStore("21082022-test2-flush-int16-values", utils.PerformanceMetric, true, true, verticalWriter.encoder, verticalWriter.tokenizers[0])

	expectedTicks := []int32{241028436, 241028441, 241028446, 241028451, 241028456, 241028461, 241028466, 241028471, 241028476, 241028481, 241028486, 241028491, 241028496, 241028506, 241028501}

	metricKey := "1^int16.metric"

	expectedValues := []float64{1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 15, 14}

	cacheValues := make([][]interface{}, 2)

	cacheValues[1] = []interface{}{Float64}

	for index := 1; index < 11; index++ { //max rle encoding values

		cacheValues[0] = append(cacheValues[0], expectedTicks[index])

		cacheValues[1] = append(cacheValues[1], expectedValues[index])
	}

	utils.MaxPoolLength = 10

	writer := NewVerticalWriter(0, []*StaticMetricWriter{staticMetricWriter})

	writer.Start()

	err := writer.flush(metricKey, store, cacheValues)

	assertions.Nil(err)

	cacheValues = make([][]interface{}, 2)

	cacheValues[1] = append(cacheValues[1], Float64)

	cacheValues[0] = append(cacheValues[0], expectedTicks[0])

	cacheValues[1] = append(cacheValues[1], expectedValues[0])

	for index := 11; index < len(expectedTicks); index++ { //max rle encoding values

		cacheValues[0] = append(cacheValues[0], expectedTicks[index])

		cacheValues[1] = append(cacheValues[1], expectedValues[index])
	}

	err = writer.flush(metricKey, store, cacheValues)

	assertions.Nil(err)

	part := UINT16ToStringValue(store.GetMaxPart(utils.GetHash64([]byte(metricKey))))

	found, valueBytes, err := store.Get([]byte(metricKey+utils.KeySeparator+part), writer.readBuffers[0], writer.encoder, writer.event, &writer.waitGroup, mockMetricTokenizer, true)

	assertions.True(found)

	assertions.Nil(err)

	assertions.NotNil(valueBytes)

	poolIndex, _, _, _, _, _, values, _, err := datastore.GetColumns(GetDataType(valueBytes[0]), valueBytes[0], valueBytes[1:], "", "", 0, decoder)

	defer decoder.MemoryPool.ReleaseFLOAT64Pool(poolIndex)

	assertions.Nil(err)

	assertions.NotNil(values)

	expectedValues = []float64{1, 12, 13, 14, 15}

	assertions.EqualValues(expectedValues, values)

	found, valueBytes, err = store.Get([]byte(metricKey+utils.KeySeparator+"time"+utils.KeySeparator+part), writer.readBuffers[1], writer.encoder, writer.event, &writer.waitGroup, mockMetricTokenizer, true)

	tickIndex, ticks, err := datastore.GetTimeTicks(valueBytes, 0, decoder)

	defer decoder.MemoryPool.ReleaseINT32Pool(tickIndex)

	assertions.NotNil(ticks)

	expectedTicks = []int32{241028436, 241028491, 241028496, 241028501, 241028506}

	assertions.EqualValues(expectedTicks, ticks)

	assertions.Nil(err)

}

func TestFlushOverflowString(t *testing.T) {

	poolLength := utils.MaxPoolLength

	utils.MaxPoolLength = 10

	defer func() {
		utils.MaxPoolLength = poolLength
	}()

	assertions := assert.New(t)

	store := datastore.GetStore("21082022-test2-flush-string-values", utils.PerformanceMetric, true, true, verticalWriter.encoder, verticalWriter.tokenizers[0])

	expectedTicks := []int32{241028436, 241028441, 241028446, 241028451, 241028456, 241028461, 241028466, 241028471, 241028476, 241028481, 241028486, 241028491, 241028496, 241028506, 241028501}

	metricKey := "1^int16.metric"

	expectedValues := []string{"metric1", "metric2", "metric3", "metric4", "metric5", "metric6", "metric7", "metric8", "metric9", "metric10", "metric11", "metric12", "metric13", "metric15", "metric14"}

	cacheValues := make([][]interface{}, 2)

	cacheValues[1] = []interface{}{String}

	for index := 1; index < 11; index++ { //max rle encoding values

		cacheValues[0] = append(cacheValues[0], expectedTicks[index])

		cacheValues[1] = append(cacheValues[1], expectedValues[index])
	}

	utils.MaxPoolLength = 10

	writer := NewVerticalWriter(0, []*StaticMetricWriter{staticMetricWriter})

	writer.Start()

	err := writer.flush(metricKey, store, cacheValues)

	assertions.Nil(err)

	cacheValues = make([][]interface{}, 2)

	cacheValues[1] = append(cacheValues[1], String)

	cacheValues[0] = append(cacheValues[0], expectedTicks[0])

	cacheValues[1] = append(cacheValues[1], expectedValues[0])

	for i := 11; i < len(expectedTicks); i++ { //max rle encoding values

		cacheValues[0] = append(cacheValues[0], expectedTicks[i])

		cacheValues[1] = append(cacheValues[1], expectedValues[i])
	}

	err = writer.flush(metricKey, store, cacheValues)

	assertions.Nil(err)

	part := UINT16ToStringValue(store.GetMaxPart(utils.GetHash64([]byte(metricKey))))

	found, valueBytes, err := store.Get([]byte(metricKey+utils.KeySeparator+part), writer.readBuffers[0], writer.encoder, writer.event, &writer.waitGroup, mockMetricTokenizer, true)

	assertions.True(found)

	assertions.Nil(err)

	assertions.NotNil(valueBytes)

	poolIndex, _, _, _, _, _, _, values, err := datastore.GetColumns(GetDataType(valueBytes[0]), valueBytes[0], valueBytes[1:], "", "", 0, decoder)

	defer decoder.MemoryPool.ReleaseStringPool(poolIndex)

	assertions.Nil(err)

	assertions.NotNil(values)

	expectedValues = []string{"metric1", "metric12", "metric13", "metric14", "metric15"}

	assertions.EqualValues(expectedValues, values)

	found, valueBytes, err = store.Get([]byte(metricKey+utils.KeySeparator+"time"+utils.KeySeparator+part), writer.readBuffers[1], writer.encoder, writer.event, &writer.waitGroup, mockMetricTokenizer, true)

	tickIndex, ticks, err := datastore.GetTimeTicks(valueBytes, 0, decoder)

	defer decoder.MemoryPool.ReleaseINT32Pool(tickIndex)

	assertions.NotNil(ticks)

	expectedTicks = []int32{241028436, 241028491, 241028496, 241028501, 241028506}

	assertions.EqualValues(expectedTicks, ticks)

	assertions.Nil(err)

}

func TestWriteStatusMetrics(t *testing.T) {

	verticalWriter = NewVerticalWriter(1, []*StaticMetricWriter{staticMetricWriter})

	assertions := assert.New(t)

	verticalWriter.Start()

	pluginName := "test"

	tick := time.Now().Unix()

	//objectId --> instance --> status -->reason -->metric-->duration

	values := []string{"1", "instance", "uptime", "", "uptime", "1234"}

	statusDurationBach := packStatusDurationBatch(pluginName, tick, values)

	statusDurationBach[datastore.DatastoreType] = utils.ObjectStatusMetric

	atomic.AddInt32(&verticalWriter.Pending, 1)

	verticalWriter.Events <- statusDurationBach

	time.Sleep(time.Second * 3)

	store := datastore.GetStore(datastore.GetStoreById(tick, pluginName, datastore.VerticalStore), utils.ObjectStatusMetric, false, true, verticalWriter.encoder, verticalWriter.tokenizers[0])

	key := "1^instance^uptime.seconds^0"

	valueBytes := make([]byte, 100)

	_ = store.Sync(encoder)

	ok, resultBytes, err := store.Get([]byte(key), valueBytes, verticalWriter.encoder, verticalWriter.event, &verticalWriter.waitGroup, verticalWriter.tokenizers[0], true)

	poolIndex, _, _, int16Values, _, _, _, _, err := datastore.GetColumns(GetDataType(resultBytes[0]), resultBytes[0], resultBytes[1:], key, store.GetName(), 0, verticalWriter.decoder)

	defer verticalWriter.decoder.MemoryPool.ReleaseINT16Pool(poolIndex)

	assertions.Nil(err)

	assertions.True(ok)

	assertions.Equal(int16(1234), int16Values[0])

}

func TestWriteStatusMetricsV1(t *testing.T) {

	assertions := assert.New(t)

	verticalWriter = NewVerticalWriter(1, []*StaticMetricWriter{staticMetricWriter})

	verticalWriter.Start()

	pluginName := "test1"

	tick := time.Now().Unix()

	//objectId --> instance --> status -->reason -->metric-->duration

	values := []string{"1", "instance", "downtime", "", "downtime", "1234"}

	statusDurationBach := packStatusDurationBatch(pluginName, tick, values)

	statusDurationBach[datastore.DatastoreType] = utils.ObjectStatusMetric

	atomic.AddInt32(&verticalWriter.Pending, 1)

	verticalWriter.Events <- statusDurationBach

	time.Sleep(time.Second * 3)

	store := datastore.GetStore(datastore.GetStoreById(tick, pluginName, datastore.VerticalStore), utils.ObjectStatusMetric, false, true, verticalWriter.encoder, verticalWriter.tokenizers[0])

	key := "1^instance^downtime.seconds^0"

	valueBytes := make([]byte, 100)

	_ = store.Sync(encoder)

	ok, resultBytes, err := store.Get([]byte(key), valueBytes, verticalWriter.encoder, verticalWriter.event, &verticalWriter.waitGroup, verticalWriter.tokenizers[0], true)

	poolIndex, _, _, int16Values, _, _, _, _, err := datastore.GetColumns(GetDataType(resultBytes[0]), resultBytes[0], resultBytes[1:], key, store.GetName(), 0, verticalWriter.decoder)

	defer verticalWriter.decoder.MemoryPool.ReleaseINT16Pool(poolIndex)

	assertions.Nil(err)

	assertions.True(ok)

	assertions.Equal(int16(1234), int16Values[0])

}

func TestWriteStatusMetricsInstanceCounter(t *testing.T) {

	assertions := assert.New(t)

	verticalWriter = NewVerticalWriter(1, []*StaticMetricWriter{staticMetricWriter})

	verticalWriter.Start()

	pluginName := "test"

	tick := time.Now().Unix()

	//objectId --> instance --> status -->reason -->column-->duration
	values := []string{"1", "instance", "uptime", "", "up~uptime", "1234"}

	statusDurationBach := packStatusDurationBatch(pluginName, tick, values)

	statusDurationBach[datastore.DatastoreType] = utils.ObjectStatusMetric

	statusDurationBach[datastore.DatastoreType] = utils.ObjectStatusMetric

	atomic.AddInt32(&verticalWriter.Pending, 1)

	verticalWriter.Events <- statusDurationBach

	time.Sleep(time.Second * 3)

	store := datastore.GetStore(datastore.GetStoreById(tick, pluginName, datastore.VerticalStore), utils.ObjectStatusMetric, false, true, verticalWriter.encoder, verticalWriter.tokenizers[0])

	key := "1^instance^up~uptime.seconds^0"

	valueBytes := make([]byte, 100)

	_ = store.Sync(encoder)

	ok, resultBytes, err := store.Get([]byte(key), valueBytes, verticalWriter.encoder, verticalWriter.event, &verticalWriter.waitGroup, verticalWriter.tokenizers[0], true)

	poolIndex, _, _, int16Values, _, _, _, _, err := datastore.GetColumns(GetDataType(resultBytes[0]), resultBytes[0], resultBytes[1:], key, store.GetName(), 0, verticalWriter.decoder)

	defer verticalWriter.decoder.MemoryPool.ReleaseINT16Pool(poolIndex)

	assertions.Nil(err)

	assertions.True(ok)

	assertions.Equal(int16(1234), int16Values[0])

}

func TestToFlOAT(t *testing.T) {

	assertions := assert.New(t)

	var value interface{}

	value = 10

	assertions.Equal(float64(10), utils.ToFlOAT(value))

	value = int8(10)

	assertions.Equal(float64(10), utils.ToFlOAT(value))

	value = int32(10)

	assertions.Equal(float64(10), utils.ToFlOAT(value))

	value = int64(10)

	assertions.Equal(float64(10), utils.ToFlOAT(value))

	value = uint32(10)

	assertions.Equal(float64(10), utils.ToFlOAT(value))

	value = uint64(10)

	assertions.Equal(float64(10), utils.ToFlOAT(value))

	value = float64(10)

	assertions.Equal(float64(10), utils.ToFlOAT(value))

	value = 10.10

	assertions.Equal(10.10, utils.ToFlOAT(value))

	value = 10.101

	assertions.Equal(10.10, utils.ToFlOAT(value))

	value = 10.105

	assertions.Equal(10.11, utils.ToFlOAT(value))

}

func TestHandleError(t *testing.T) {

	defer func() {
		if r := recover(); r != nil {

			assert.NotNil(t, r)

		}
	}()

	var err error

	err = os.ErrInvalid

	handleError(err)

}

func TestPanicDataReader(t *testing.T) {

	assertions := assert.New(t)

	_ = os.RemoveAll(utils.EventDir)

	utils.Create(utils.TempDir)

	utils.Create(utils.TempDir + utils.HyphenSeparator + utils.Aggregations)

	utils.Create(utils.EventDir + utils.PathSeparator + utils.VerticalFormat)

	utils.Create(utils.EventDir + utils.PathSeparator + utils.HorizontalFormat)

	utils.Create(utils.EventDir + utils.PathSeparator + utils.HorizontalFormat + utils.HyphenSeparator + utils.Aggregations)

	err := os.WriteFile(utils.EventDir+utils.PathSeparator+utils.HorizontalFormat+utils.PathSeparator+"1701254778§50001-log§11§1§0§1701254780591958350", make([]byte, 500), 0666)

	assertions.Nil(err)

	horizontalWriter1 := &HorizontalWriter{}

	reader := NewDataReader(nil, []*HorizontalWriter{horizontalWriter1}, nil, nil)

	reader.dir = utils.EventDir + utils.PathSeparator + reader.readerType

	go reader.process()

	utils.DatastoreFlushTimerSeconds = 2

	utils.DatastoreBrokerWriterFlushTimerSeconds = 2

	time.Sleep(time.Second * 6)

	bufferBytes, err := utils.ReadLogFile("Data Reader", "writer")

	assertions.Nil(err)

	assertions.True(strings.Contains(string(bufferBytes), "!!!STACK TRACE for data reader!!!"))

	reader.ShutdownNotifications <- true
}

func TestFileRead(t *testing.T) {

	assertions := assert.New(t)

	fileName := "verticalFile"

	utils.VerticalFormatSyncNotifications = make(chan utils.WriterSyncEvent, 10)

	utils.HorizontalFormatSyncNotifications = make(chan utils.WriterSyncEvent, 10)

	_ = os.RemoveAll(verticalEvents)

	_ = os.RemoveAll(horizontalEvents)

	utils.Create(verticalEvents)

	utils.Create(horizontalEvents)

	bufferBytes := make([]byte, len(verticalWriter.valueBufferBytes)+100)

	qualifiedTime := time.Now().UTC()

	qualifiedDate := INT64ToStringValue(time.Date(qualifiedTime.Year(), qualifiedTime.Month(),
		qualifiedTime.Day(), 0, 0, 0, 0, time.UTC).Unix())

	err := os.MkdirAll(verticalEvents+utils.PathSeparator+qualifiedDate, 0755)

	assertions.Nil(err)

	filePath := verticalEvents + utils.PathSeparator + qualifiedDate + utils.PathSeparator + fileName

	err = os.WriteFile(filePath, bufferBytes, 0666)

	assertions.Nil(err)

	err = verticalWriter.read(filePath)

	assertions.Nil(err)

	assertions.Equal(bufferBytes, verticalWriter.bufferBytes)

	err = os.MkdirAll(horizontalEvents+utils.PathSeparator+qualifiedDate, 0755)

	assertions.Nil(err)

	fileName = "horizontalfile"

	filePath = horizontalEvents + utils.PathSeparator + qualifiedDate + utils.PathSeparator + fileName

	err = os.WriteFile(filePath, bufferBytes, 0666)

	assertions.Nil(err)

	err = horizontalWriter.read(filePath)

	assertions.Nil(err)

	assertions.Equal(bufferBytes, horizontalWriter.bufferBytes)

}

func TestHorizontalWriterPanicRecover(t *testing.T) {

	writer := NewHorizontalWriter(20)

	writer.Start()

	atomic.AddInt32(&writer.Pending, 1)

	writer.Events <- utils.MotadataMap{

		utils.Plugin: "dummy",
		utils.Buffer: "d",
	}

	time.Sleep(time.Second * 1)

	bytes, err := utils.ReadLogFile("Horizontal Writer", "writer")

	assert.Nil(t, err)

	assert.NotNil(t, bytes)

	assert.Contains(t, string(bytes), "error interface conversion: interface {} is nil, not utils.DatastoreType occurred in horizontal writer")

	writer.ShutdownNotifications <- true
}

func TestNotifyIndexer(t *testing.T) {

	utils.IndexJobRequests = make(chan utils.MotadataMap, 1000)

	writer := NewHorizontalWriter(20)

	writer.int64Fields["demo"] = nil

	writer.int64Fields[utils.MaxSuffix] = nil

	writer.int32Fields["demo"] = nil

	writer.int32Fields[utils.MaxSuffix] = nil

	writer.stringFields["dummy-bytes"] = nil

	writer.notifyIndexer()

	columns := <-utils.IndexJobRequests

	assert.Greater(t, len(columns[utils.Columns].(map[string]interface{})), 0)

}

func TestHorizontalRead(t *testing.T) {

	fileName := "dummy-test-file"

	writer := NewHorizontalWriter(20)

	writer.dataStoreType = utils.Log

	err := writer.read(horizontalEvents + utils.PathSeparator + fileName)

	assert.NotNil(t, err)

}

func TestVerticalWriterPanicRecover(t *testing.T) {

	writer := NewVerticalWriter(20, []*StaticMetricWriter{staticMetricWriter})

	defer func() {
		writer.ShutdownNotifications <- true
	}()

	writer.Start()

	atomic.AddInt32(&writer.Pending, 1)

	writer.Events <- utils.MotadataMap{

		utils.Plugin: "",
	}

	time.Sleep(time.Second * 3)

	bytes, err := utils.ReadLogFile("Vertical Writer", "writer")

	assert.Nil(t, err)

	assert.Contains(t, string(bytes), "error interface conversion: interface {} is nil, not utils.DatastoreType occurred in vertical writer")

}

func TestNilEventVerticalWriter(t *testing.T) {

	writer := NewVerticalWriter(20, []*StaticMetricWriter{staticMetricWriter})

	atomic.AddInt32(&writer.Pending, 1)

	writer.writeVerticalBatch(utils.MotadataMap{})

	atomic.AddInt32(&writer.Pending, 1)

	writer.writeVerticalBatch(utils.MotadataMap{datastore.DatastoreType: utils.PerformanceMetric})

	atomic.AddInt32(&writer.Pending, 1)

	writer.writeVerticalBatch(utils.MotadataMap{utils.Plugin: "", utils.File: "dummy", datastore.DatastoreType: utils.PerformanceMetric})

	bytes, err := utils.ReadLogFile("Vertical Writer", "writer")

	assert.Nil(t, err)

	assert.Contains(t, string(bytes), "no such file or directory occurred while read batch file dummy")

	atomic.AddInt32(&writer.Pending, 1)

	writer.writeVerticalBatch(utils.MotadataMap{utils.Plugin: "", datastore.DatastoreType: utils.DatastoreType(200), utils.Buffer: []byte{}})

	bytes, err = utils.ReadLogFile("Vertical Writer", "writer")

	assert.Nil(t, err)

	assert.Contains(t, string(bytes), "invalid batch received at vertical writer")

}

func TestAggregateRead(t *testing.T) {

	maxBlobBytes := utils.MaxBlobBytes

	defer func() {

		utils.MaxBlobBytes = maxBlobBytes
	}()

	utils.MaxBlobBytes = 0

	aggregationId := 22

	aggregator := NewEventAggregator(aggregationId)

	fileName := "dummy-file-Name"

	os.MkdirAll(workingDir, 0777)

	file, _ := os.Create(workingDir + fileName)

	file.Write([]byte("dummy data"))

	_, err := aggregator.read(workingDir + fileName)

	assert.NotNil(t, err)

}

func TestAggregateV2(t *testing.T) {

	utils.EventAggregationSyncNotifications = make(chan utils.EventAggregationSyncEvent, 5)

	defer func() {

		utils.EventAggregationSyncNotifications = nil
	}()

	qualifiedTime := time.Now().UTC()

	qualifiedDate := INT64ToStringValue(time.Date(qualifiedTime.Year(), qualifiedTime.Month(),
		qualifiedTime.Day(), 0, 0, 0, 0, time.UTC).Unix())

	err := os.MkdirAll(workingDir+qualifiedDate, 0777)

	assert.Nil(t, err)

	aggregatorId := 21

	tick := time.Now().Unix()

	fileName := "demo-testing-event-aggregation-2"

	fileName = utils.INT64ToStringValue(tick) + utils.SpecialSeparator + fileName

	event := utils.MotadataMap{

		utils.File: qualifiedDate + utils.PathSeparator + fileName,
	}

	aggregator := NewEventAggregator(aggregatorId)

	aggregator.Start()

	defer func() {

		aggregator.ShutdownNotifications <- true
	}()

	file, err := os.Create(workingDir + qualifiedDate + utils.PathSeparator + fileName)

	_, err = file.Write(gozstd.Compress(nil, []byte("dummy data")))

	_ = file.Close()

	atomic.AddInt32(&aggregator.PendingRequests, 1)

	aggregator.Events <- event

	time.Sleep(time.Second * 2)

	bytes, err := utils.ReadLogFile(fmt.Sprintf("Event Aggregator %v", aggregatorId), "aggregator")

	assert.Nil(t, err)

	assert.Contains(t, string(bytes), "invalid/corrupted buffer bytes")

}

func TestAggregateV3(t *testing.T) {

	utils.EventAggregationSyncNotifications = make(chan utils.EventAggregationSyncEvent, 5)

	defer func() {

		utils.EventAggregationSyncNotifications = nil
	}()

	os.MkdirAll(workingDir, 0777)

	utils.SetLogLevel(utils.LogLevelTrace)

	fileName := "demo-testing-event-aggregation-1"

	aggregatorId := 20

	tick := time.Now().Unix()

	event := utils.MotadataMap{

		utils.File: utils.INT64ToStringValue(tick) + utils.SpecialSeparator + fileName,
	}

	aggregator := NewEventAggregator(aggregatorId)

	aggregator.Start()

	defer func() {

		aggregator.ShutdownNotifications <- true
	}()

	key := fileName + utils.KeySeparator + utils.INT64ToStringValue(utils.GetBaseTickv1(tick)) + utils.KeySeparator + "" + utils.KeySeparator + utils.HorizontalFormat

	atomic.AddInt32(&aggregator.PendingRequests, 1)

	aggregator.Events <- event

	time.Sleep(time.Second * 2)

	bytes, err := utils.ReadLogFile(fmt.Sprintf("Event Aggregator %v", aggregatorId), "aggregator")

	assert.Nil(t, err)

	assert.Contains(t, string(bytes), "occurred while aggregating file")

	datastore.UpdateAggregationContexts(key, 0, utils.Add)

	atomic.AddInt32(&aggregator.PendingRequests, 1)

	aggregator.Events <- event

	time.Sleep(time.Second * 2)

	bytes, err = utils.ReadLogFile(fmt.Sprintf("Event Aggregator %v", aggregatorId), "aggregator")

	assert.Nil(t, err)

	assert.Contains(t, string(bytes), "tick record as it is being aggregated by the offline job")

}

func TestAggregateMerge(t *testing.T) {

	aggregatorId := 23

	aggregator := NewEventAggregator(aggregatorId)

	aggregator.encoder.MemoryPool = nil

	aggregator.merge(&Store{})

	bytes, err := utils.ReadLogFile(fmt.Sprintf("Event Aggregator %v", aggregatorId), "aggregator")

	assert.Nil(t, err)

	assert.Contains(t, string(bytes), "occurred while merging groups for store")

}

func TestAggregateAppend(t *testing.T) {

	aggregatorId := 24

	aggregator := NewEventAggregator(aggregatorId)

	aggregator.encoder.MemoryPool = nil

	aggregator.groups = nil

	aggregator.append(&Store{}, 0, 0, 0, false)

	bytes, err := utils.ReadLogFile(fmt.Sprintf("Event Aggregator %v", aggregatorId), "aggregator")

	assert.Nil(t, err)

	assert.Contains(t, string(bytes), "occurred while appending new groups for store")

}

func TestVerticalWriterProcessPerformanceBatch(t *testing.T) {

	verticalWriterId := 25

	verticalWriter := NewVerticalWriter(verticalWriterId, []*StaticMetricWriter{staticMetricWriter})

	verticalWriter.bufferBytes = nil

	verticalWriter.processPerformanceBatch(0, 0)

	bytes, err := utils.ReadLogFile("Vertical Writer", "writer")

	assert.Nil(t, err)

	assert.Contains(t, string(bytes), "occurred while decode store type")

}

func TestVerticalWriterProcessPerformanceBatchV2(t *testing.T) {

	verticalWriterId := 25

	verticalWriter := NewVerticalWriter(verticalWriterId, []*StaticMetricWriter{staticMetricWriter})

	data := []utils.MotadataMap{
		{
			"datatype":  datastore.StringColumn,
			"object.id": 1,
			"metric":    "",
			"instance":  "instance",
			"value":     "demo",
		},
	}

	buffer := bytes2.Buffer{}

	dataBlock := make(map[string][]utils.MotadataMap)

	dataBlock["1"+utils.GroupSeparator+"instance"] = data

	PackPerformanceMetricDataBlockV1(dataBlock, &buffer)

	verticalWriter.bufferBytes = buffer.Bytes()

	length := int(binary.LittleEndian.Uint32(verticalWriter.bufferBytes[verticalWriter.position : verticalWriter.position+4]))

	verticalWriter.position += 4

	verticalWriter.processPerformanceBatch(length, verticalWriter.position)

	data = []utils.MotadataMap{
		{
			"datatype":  datastore.StringColumn,
			"object.id": 1,
			"metric":    "ddd",
			"instance":  "instance",
			"value":     "demo",
		},
	}

	buffer = bytes2.Buffer{}

	dataBlock = make(map[string][]utils.MotadataMap)

	dataBlock["1"+utils.GroupSeparator+"instance"] = data

	PackPerformanceMetricDataBlockV1(dataBlock, &buffer)

	verticalWriter.bufferBytes = buffer.Bytes()

	verticalWriter.plugin = "demo-plugin-1"

	poolLength := utils.MaxPoolLength

	utils.MaxPoolLength = 10

	store, err := OpenOrCreateStore(verticalWriter.plugin, utils.StaticMetric, encoder, tokenizer, false)

	assert.Nil(t, err)

	assert.NotNil(t, store)

	store.Close(encoder)

	datastore.RemoveStore(store.GetName())

	utils.MaxPoolLength = poolLength

	length = int(binary.LittleEndian.Uint32(verticalWriter.bufferBytes[verticalWriter.position : verticalWriter.position+4]))

	verticalWriter.position = 0

	verticalWriter.position += 4

	verticalWriter.tokenizers[1] = &utils.Tokenizer{

		Tokens: make([]string, bufio.MaxScanTokenSize),
	}

	verticalWriter.processPerformanceBatch(length, verticalWriter.position)

	bytes, err := utils.ReadLogFile("Vertical Writer", "writer")

	assert.Nil(t, err)

	assert.Contains(t, string(bytes), "occurred while decode store type")

}

func TestDataReaderFileDistributionVersion1(t *testing.T) {

	_ = os.RemoveAll(utils.EventDir)

	_ = os.MkdirAll(utils.EventDir+utils.PathSeparator+utils.VerticalFormat+utils.PathSeparator+"1", 0755)

	_ = os.MkdirAll(utils.EventDir+utils.PathSeparator+utils.VerticalFormat+utils.PathSeparator+"11", 0755)

	_ = os.MkdirAll(utils.EventDir+utils.PathSeparator+utils.VerticalFormat+utils.PathSeparator+"2", 0755)

	utils.MaxCurrentDayEventFileQueueSize = 4

	utils.MaxPreviousDayEventFileQueueSize = 2

	writer := 0

	for i := 0; i < 20; i++ {

		_ = os.WriteFile(utils.EventDir+utils.PathSeparator+utils.VerticalFormat+utils.PathSeparator+"11"+utils.PathSeparator+INTToStringValue(i)+utils.SpecialSeparator+INTToStringValue(writer), []byte(""), 0666)

		writer++

		if writer == utils.VerticalWriters {

			writer = 0
		}
	}

	writer = 0

	for i := 20; i < 40; i++ {

		_ = os.WriteFile(utils.EventDir+utils.PathSeparator+utils.VerticalFormat+utils.PathSeparator+"2"+utils.PathSeparator+INTToStringValue(i)+utils.SpecialSeparator+INTToStringValue(writer), []byte(""), 0666)

		writer++

		if writer == utils.VerticalWriters {

			writer = 0
		}
	}

	writer = 0

	for i := 40; i < 48; i++ {

		_ = os.WriteFile(utils.EventDir+utils.PathSeparator+utils.VerticalFormat+utils.PathSeparator+"1"+utils.PathSeparator+INTToStringValue(i)+utils.SpecialSeparator+INTToStringValue(writer), []byte(""), 0666)

		writer++

		if writer == utils.VerticalWriters {

			writer = 0
		}
	}

	reader := NewDataReader(make([]*VerticalWriter, utils.VerticalWriters), nil, nil, nil)

	reader.dir = utils.EventDir + utils.PathSeparator + utils.VerticalFormat

	reader.readers = make([]*bitmap.Bitmap, utils.VerticalWriters)

	for index := range reader.readers {

		reader.readers[index] = &bitmap.Bitmap{}
	}

	reader.tokenizer = &utils.Tokenizer{

		Tokens: make([]string, 50),
	}

	reader.getFiles()

	assertions := assert.New(t)

	files := map[string]struct{}{}

	counts := []int{4, 2, 2} //according to 3 partitions

	partitions := []string{"11", "2", "1"} //according to 3 partitions

	count := 0

	partitionFlushes := []bool{false, false, false}

	k := 0

	var queuedFiles []string

	for _, file := range reader.files {

		flushed := true

		for _, partitionFlush := range partitionFlushes {

			if !partitionFlush {

				flushed = false

				break
			}

		}

		if flushed {

			break
		}

		utils.Split(file, utils.PathSeparator, tokenizer)

		if file != utils.Empty && (partitionFlushes[k] || tokenizer.Tokens[tokenizer.Counts-2] == partitions[k]) {

			count++

			if tokenizer.Tokens[tokenizer.Counts-2] == partitions[k] {

				assertions.False(partitionFlushes[k])

			}

			queuedFiles = append(queuedFiles, file)

		} else if file == utils.Empty {

			if counts[k] == count {

				assertions.True(verifyFileQueueOrder(queuedFiles))

				queuedFiles = nil

				k++

				if k == len(counts) {

					k = 0
				}

				count = 0
			}

			count++

			partitionFlushes[k] = true

			continue

		} else {

			assertions.Equal(counts[k], count)

			assertions.True(verifyFileQueueOrder(queuedFiles))

			queuedFiles = nil

			k++

			if k == len(counts) {

				k = 0
			}

			count = 0

			utils.Split(file, utils.PathSeparator, tokenizer)

			if tokenizer.Tokens[tokenizer.Counts-2] == partitions[k] {

				count++

				assertions.False(partitionFlushes[k])

				queuedFiles = append(queuedFiles, file)
			}
		}

		files[file] = struct{}{}
	}

	assertions.Equal(48, len(files))

}

func verifyFileQueueOrder(queuedFiles []string) bool {

	var values []int

	for index := range queuedFiles {

		utils.Split(queuedFiles[index], utils.PathSeparator, tokenizer)

		utils.Split(tokenizer.Tokens[tokenizer.Counts-1], utils.SpecialSeparator, tokenizer)

		num, _ := strconv.Atoi(tokenizer.Tokens[0])

		values = append(values, num)
	}

	return sort.IntsAreSorted(values)

}

func TestDataReaderFileDistributionVersion2(t *testing.T) {

	_ = os.RemoveAll(utils.EventDir)

	_ = os.MkdirAll(utils.EventDir+utils.PathSeparator+utils.VerticalFormat+utils.PathSeparator+"1", 0755)

	_ = os.MkdirAll(utils.EventDir+utils.PathSeparator+utils.VerticalFormat+utils.PathSeparator+"11", 0755)

	_ = os.MkdirAll(utils.EventDir+utils.PathSeparator+utils.VerticalFormat+utils.PathSeparator+"2", 0755)

	utils.MaxCurrentDayEventFileQueueSize = 4

	utils.MaxPreviousDayEventFileQueueSize = 2

	writer := 0

	for i := 0; i < 20; i++ {

		_ = os.WriteFile(utils.EventDir+utils.PathSeparator+utils.VerticalFormat+utils.PathSeparator+"11"+utils.PathSeparator+INTToStringValue(i)+utils.SpecialSeparator+INTToStringValue(writer), []byte(""), 0666)

		writer++

		if writer == utils.VerticalWriters {

			writer = 0
		}
	}

	writer = 0

	for i := 20; i < 40; i++ {

		_ = os.WriteFile(utils.EventDir+utils.PathSeparator+utils.VerticalFormat+utils.PathSeparator+"2"+utils.PathSeparator+INTToStringValue(i)+utils.SpecialSeparator+INTToStringValue(writer), []byte(""), 0666)

		writer++

		if writer == utils.VerticalWriters {

			writer = 0
		}
	}

	reader := NewDataReader(make([]*VerticalWriter, utils.VerticalWriters), nil, nil, nil)

	reader.dir = utils.EventDir + utils.PathSeparator + utils.VerticalFormat

	reader.readers = make([]*bitmap.Bitmap, utils.VerticalWriters)

	for index := range reader.readers {

		reader.readers[index] = &bitmap.Bitmap{}
	}

	reader.tokenizer = &utils.Tokenizer{

		Tokens: make([]string, 50),
	}

	reader.getFiles()

	assertions := assert.New(t)

	files := map[string]struct{}{}

	count := 0

	k := 0

	var queuedFiles []string

	counts := []int{4, 2, 2} //according to 3 partitions

	partitions := []string{"11", "2", "1"} //according to 3 partitions

	partitionFlushes := []bool{false, false, false}

	for _, file := range reader.files {

		flushed := true

		for _, partitionFlush := range partitionFlushes {

			if !partitionFlush {

				flushed = false

				break
			}

		}

		if flushed {

			break
		}

		utils.Split(file, utils.PathSeparator, tokenizer)

		if file != utils.Empty && (partitionFlushes[k] || tokenizer.Tokens[tokenizer.Counts-2] == partitions[k]) {

			count++

			if tokenizer.Tokens[tokenizer.Counts-2] == partitions[k] {

				assertions.False(partitionFlushes[k])

			}

			queuedFiles = append(queuedFiles, file)

		} else if file == utils.Empty {

			if counts[k] == count {

				assertions.True(verifyFileQueueOrder(queuedFiles))

				queuedFiles = nil

				k++

				if k == len(counts) {

					k = 0
				}

				count = 0
			}

			count++

			partitionFlushes[k] = true

			continue

		} else {

			assertions.Equal(counts[k], count)

			assertions.True(verifyFileQueueOrder(queuedFiles))

			queuedFiles = nil

			k++

			if k == len(counts) {

				k = 0
			}

			count = 0

			utils.Split(file, utils.PathSeparator, tokenizer)

			if tokenizer.Tokens[tokenizer.Counts-2] == partitions[k] {

				count++

				assertions.False(partitionFlushes[k])

				queuedFiles = append(queuedFiles, file)
			}
		}

		files[file] = struct{}{}
	}

	assertions.Equal(40, len(files))

}

func TestDataReaderFileDistributionVersion3(t *testing.T) {

	_ = os.RemoveAll(utils.EventDir)

	_ = os.MkdirAll(utils.EventDir+utils.PathSeparator+utils.VerticalFormat+utils.PathSeparator+"1", 0755)

	_ = os.MkdirAll(utils.EventDir+utils.PathSeparator+utils.VerticalFormat+utils.PathSeparator+"11", 0755)

	_ = os.MkdirAll(utils.EventDir+utils.PathSeparator+utils.VerticalFormat+utils.PathSeparator+"12", 0755)

	_ = os.MkdirAll(utils.EventDir+utils.PathSeparator+utils.VerticalFormat+utils.PathSeparator+"2", 0755)

	_ = os.MkdirAll(utils.EventDir+utils.PathSeparator+utils.VerticalFormat+utils.PathSeparator+"3", 0755)

	utils.MaxCurrentDayEventFileQueueSize = 2

	utils.MaxPreviousDayEventFileQueueSize = 1

	writer := 0

	for i := 0; i < 20; i++ {

		_ = os.WriteFile(utils.EventDir+utils.PathSeparator+utils.VerticalFormat+utils.PathSeparator+"12"+utils.PathSeparator+INTToStringValue(i)+utils.SpecialSeparator+INTToStringValue(writer), []byte(""), 0666)

		writer++

		if writer == utils.VerticalWriters {

			writer = 0
		}
	}

	writer = 0

	for i := 20; i < 40; i++ {

		_ = os.WriteFile(utils.EventDir+utils.PathSeparator+utils.VerticalFormat+utils.PathSeparator+"11"+utils.PathSeparator+INTToStringValue(i)+utils.SpecialSeparator+INTToStringValue(writer), []byte(""), 0666)

		writer++

		if writer == utils.VerticalWriters {

			writer = 0
		}
	}

	writer = 0

	for i := 40; i < 45; i++ {

		_ = os.WriteFile(utils.EventDir+utils.PathSeparator+utils.VerticalFormat+utils.PathSeparator+"3"+utils.PathSeparator+INTToStringValue(i)+utils.SpecialSeparator+INTToStringValue(writer), []byte(""), 0666)

		writer++

		if writer == utils.VerticalWriters {

			writer = 0
		}
	}

	writer = 0

	for i := 45; i < 70; i++ {

		_ = os.WriteFile(utils.EventDir+utils.PathSeparator+utils.VerticalFormat+utils.PathSeparator+"2"+utils.PathSeparator+INTToStringValue(i)+utils.SpecialSeparator+INTToStringValue(writer), []byte(""), 0666)

		writer++

		if writer == utils.VerticalWriters {

			writer = 0
		}
	}

	writer = 0

	for i := 70; i < 120; i++ {

		_ = os.WriteFile(utils.EventDir+utils.PathSeparator+utils.VerticalFormat+utils.PathSeparator+"1"+utils.PathSeparator+INTToStringValue(i)+utils.SpecialSeparator+INTToStringValue(writer), []byte(""), 0666)

		writer++

		if writer == utils.VerticalWriters {

			writer = 0
		}
	}

	reader := NewDataReader(make([]*VerticalWriter, utils.VerticalWriters), nil, nil, nil)

	reader.dir = utils.EventDir + utils.PathSeparator + utils.VerticalFormat

	reader.readers = make([]*bitmap.Bitmap, utils.VerticalWriters)

	for index := range reader.readers {

		reader.readers[index] = &bitmap.Bitmap{}
	}

	reader.tokenizer = &utils.Tokenizer{

		Tokens: make([]string, 50),
	}

	reader.getFiles()

	assertions := assert.New(t)

	files := map[string]struct{}{}

	count := 0

	k := 0

	var queuedFiles []string

	counts := []int{2, 1, 1, 1, 1} //according to 3 partitions

	partitions := []string{"12", "11", "3", "2", "1"} //according to 3 partitions

	partitionFlushes := []bool{false, false, false, false, false}

	for _, file := range reader.files {

		flushed := true

		for _, partitionFlush := range partitionFlushes {

			if !partitionFlush {

				flushed = false

				break
			}

		}

		if flushed {

			break
		}

		utils.Split(file, utils.PathSeparator, tokenizer)

		if file != utils.Empty && (partitionFlushes[k] || tokenizer.Tokens[tokenizer.Counts-2] == partitions[k]) {

			count++

			if tokenizer.Tokens[tokenizer.Counts-2] == partitions[k] {

				assertions.False(partitionFlushes[k])

			}

			queuedFiles = append(queuedFiles, file)

		} else if file == utils.Empty {

			if counts[k] == count {

				assertions.True(verifyFileQueueOrder(queuedFiles))

				queuedFiles = nil

				k++

				if k == len(counts) {

					k = 0
				}

				count = 0
			}

			count++

			partitionFlushes[k] = true

			continue

		} else {

			assertions.Equal(counts[k], count)

			assertions.True(verifyFileQueueOrder(queuedFiles))

			queuedFiles = nil

			k++

			if k == len(counts) {

				k = 0
			}

			count = 0

			utils.Split(file, utils.PathSeparator, tokenizer)

			if tokenizer.Tokens[tokenizer.Counts-2] == partitions[k] {

				count++

				assertions.False(partitionFlushes[k])

				queuedFiles = append(queuedFiles, file)
			}
		}

		files[file] = struct{}{}
	}

	assertions.Equal(120, len(files))

}

func TestDataReaderFileDistributionVersion4(t *testing.T) {

	_ = os.RemoveAll(utils.EventDir)

	_ = os.MkdirAll(utils.EventDir+utils.PathSeparator+utils.VerticalFormat+utils.PathSeparator+"1", 0755)

	_ = os.MkdirAll(utils.EventDir+utils.PathSeparator+utils.VerticalFormat+utils.PathSeparator+"12", 0755)

	_ = os.MkdirAll(utils.EventDir+utils.PathSeparator+utils.VerticalFormat+utils.PathSeparator+"11", 0755)

	_ = os.MkdirAll(utils.EventDir+utils.PathSeparator+utils.VerticalFormat+utils.PathSeparator+"2", 0755)

	_ = os.MkdirAll(utils.EventDir+utils.PathSeparator+utils.VerticalFormat+utils.PathSeparator+"3", 0755)

	utils.MaxCurrentDayEventFileQueueSize = 1

	utils.MaxPreviousDayEventFileQueueSize = 1

	writer := 0

	for i := 0; i < 20; i++ {

		_ = os.WriteFile(utils.EventDir+utils.PathSeparator+utils.VerticalFormat+utils.PathSeparator+"12"+utils.PathSeparator+INTToStringValue(i)+utils.SpecialSeparator+INTToStringValue(writer), []byte(""), 0666)

		writer++

		if writer == utils.VerticalWriters {

			writer = 0
		}
	}

	writer = 0

	for i := 20; i < 40; i++ {

		_ = os.WriteFile(utils.EventDir+utils.PathSeparator+utils.VerticalFormat+utils.PathSeparator+"11"+utils.PathSeparator+INTToStringValue(i)+utils.SpecialSeparator+INTToStringValue(writer), []byte(""), 0666)

		writer++

		if writer == utils.VerticalWriters {

			writer = 0
		}
	}

	writer = 0

	for i := 40; i < 45; i++ {

		_ = os.WriteFile(utils.EventDir+utils.PathSeparator+utils.VerticalFormat+utils.PathSeparator+"3"+utils.PathSeparator+INTToStringValue(i)+utils.SpecialSeparator+INTToStringValue(writer), []byte(""), 0666)

		writer++

		if writer == utils.VerticalWriters {

			writer = 0
		}
	}

	writer = 0

	for i := 45; i < 70; i++ {

		_ = os.WriteFile(utils.EventDir+utils.PathSeparator+utils.VerticalFormat+utils.PathSeparator+"2"+utils.PathSeparator+INTToStringValue(i)+utils.SpecialSeparator+INTToStringValue(writer), []byte(""), 0666)

		writer++

		if writer == utils.VerticalWriters {

			writer = 0
		}
	}

	writer = 0

	for i := 70; i < 120; i++ {

		_ = os.WriteFile(utils.EventDir+utils.PathSeparator+utils.VerticalFormat+utils.PathSeparator+"1"+utils.PathSeparator+INTToStringValue(i)+utils.SpecialSeparator+INTToStringValue(writer), []byte(""), 0666)

		writer++

		if writer == utils.VerticalWriters {

			writer = 0
		}
	}

	reader := NewDataReader(make([]*VerticalWriter, utils.VerticalWriters), nil, nil, nil)

	reader.dir = utils.EventDir + utils.PathSeparator + utils.VerticalFormat

	reader.readers = make([]*bitmap.Bitmap, utils.VerticalWriters)

	for index := range reader.readers {

		reader.readers[index] = &bitmap.Bitmap{}
	}

	reader.tokenizer = &utils.Tokenizer{

		Tokens: make([]string, 50),
	}

	reader.getFiles()

	assertions := assert.New(t)

	files := map[string]struct{}{}

	count := 0

	k := 0

	var queuedFiles []string

	counts := []int{1, 1, 1, 1, 1} //according to 3 partitions

	partitions := []string{"12", "11", "3", "2", "1"} //according to 3 partitions

	partitionFlushes := []bool{false, false, false, false, false}

	for _, file := range reader.files {

		flushed := true

		for _, partitionFlush := range partitionFlushes {

			if !partitionFlush {

				flushed = false

				break
			}

		}

		if flushed {

			break
		}

		utils.Split(file, utils.PathSeparator, tokenizer)

		if file != utils.Empty && (partitionFlushes[k] || tokenizer.Tokens[tokenizer.Counts-2] == partitions[k]) {

			count++

			if tokenizer.Tokens[tokenizer.Counts-2] == partitions[k] {

				assertions.False(partitionFlushes[k])

			}

			queuedFiles = append(queuedFiles, file)

		} else if file == utils.Empty {

			if counts[k] == count {

				assertions.True(verifyFileQueueOrder(queuedFiles))

				queuedFiles = nil

				k++

				if k == len(counts) {

					k = 0
				}

				count = 0
			}

			count++

			partitionFlushes[k] = true

			continue

		} else {

			assertions.Equal(counts[k], count)

			assertions.True(verifyFileQueueOrder(queuedFiles))

			queuedFiles = nil

			k++

			if k == len(counts) {

				k = 0
			}

			count = 0

			utils.Split(file, utils.PathSeparator, tokenizer)

			if tokenizer.Tokens[tokenizer.Counts-2] == partitions[k] {

				count++

				assertions.False(partitionFlushes[k])

				queuedFiles = append(queuedFiles, file)
			}
		}

		files[file] = struct{}{}
	}

	assertions.Equal(120, len(files))

}

func TestDataReaderFileDistributionVersion5(t *testing.T) {

	_ = os.RemoveAll(utils.EventDir)

	_ = os.MkdirAll(utils.EventDir+utils.PathSeparator+utils.VerticalFormat+utils.PathSeparator+"1", 0755)

	utils.MaxCurrentDayEventFileQueueSize = 10

	utils.MaxPreviousDayEventFileQueueSize = 1

	writer := 0

	for i := 0; i < 20; i++ {

		_ = os.WriteFile(utils.EventDir+utils.PathSeparator+utils.VerticalFormat+utils.PathSeparator+"1"+utils.PathSeparator+INTToStringValue(i)+utils.SpecialSeparator+INTToStringValue(writer), []byte(""), 0666)

		writer++

		if writer == utils.VerticalWriters {

			writer = 0
		}
	}

	writer = 0

	for i := 20; i < 40; i++ {

		_ = os.WriteFile(utils.EventDir+utils.PathSeparator+utils.VerticalFormat+utils.PathSeparator+"1"+utils.PathSeparator+INTToStringValue(i)+utils.SpecialSeparator+INTToStringValue(writer), []byte(""), 0666)

		writer++

		if writer == utils.VerticalWriters {

			writer = 0
		}
	}

	writer = 0

	for i := 40; i < 45; i++ {

		_ = os.WriteFile(utils.EventDir+utils.PathSeparator+utils.VerticalFormat+utils.PathSeparator+"1"+utils.PathSeparator+INTToStringValue(i)+utils.SpecialSeparator+INTToStringValue(writer), []byte(""), 0666)

		writer++

		if writer == utils.VerticalWriters {

			writer = 0
		}
	}

	writer = 0

	for i := 45; i < 70; i++ {

		_ = os.WriteFile(utils.EventDir+utils.PathSeparator+utils.VerticalFormat+utils.PathSeparator+"1"+utils.PathSeparator+INTToStringValue(i)+utils.SpecialSeparator+INTToStringValue(writer), []byte(""), 0666)

		writer++

		if writer == utils.VerticalWriters {

			writer = 0
		}
	}

	writer = 0

	for i := 70; i < 120; i++ {

		_ = os.WriteFile(utils.EventDir+utils.PathSeparator+utils.VerticalFormat+utils.PathSeparator+"1"+utils.PathSeparator+INTToStringValue(i)+utils.SpecialSeparator+INTToStringValue(writer), []byte(""), 0666)

		writer++

		if writer == utils.VerticalWriters {

			writer = 0
		}
	}

	reader := NewDataReader(make([]*VerticalWriter, utils.VerticalWriters), nil, nil, nil)

	reader.dir = utils.EventDir + utils.PathSeparator + utils.VerticalFormat

	reader.readers = make([]*bitmap.Bitmap, utils.VerticalWriters)

	for index := range reader.readers {

		reader.readers[index] = &bitmap.Bitmap{}
	}

	reader.tokenizer = &utils.Tokenizer{

		Tokens: make([]string, 50),
	}

	reader.getFiles()

	assertions := assert.New(t)

	files := map[string]struct{}{}

	count := 0

	k := 0

	var queuedFiles []string

	counts := []int{10} //according to 3 partitions

	partitions := []string{"1"} //according to 3 partitions

	partitionFlushes := []bool{false}

	for _, file := range reader.files {

		flushed := true

		for _, partitionFlush := range partitionFlushes {

			if !partitionFlush {

				flushed = false

				break
			}

		}

		if flushed {

			break
		}

		utils.Split(file, utils.PathSeparator, tokenizer)

		if file != utils.Empty && (partitionFlushes[k] || tokenizer.Tokens[tokenizer.Counts-2] == partitions[k]) {

			count++

			if tokenizer.Tokens[tokenizer.Counts-2] == partitions[k] {

				assertions.False(partitionFlushes[k])

			}

			queuedFiles = append(queuedFiles, file)

		} else if file == utils.Empty {

			if counts[k] == count {

				assertions.True(verifyFileQueueOrder(queuedFiles))

				queuedFiles = nil

				k++

				if k == len(counts) {

					k = 0
				}

				count = 0
			}

			count++

			partitionFlushes[k] = true

			continue

		} else {

			assertions.Equal(counts[k], count)

			assertions.True(verifyFileQueueOrder(queuedFiles))

			queuedFiles = nil

			k++

			if k == len(counts) {

				k = 0
			}

			count = 0

			utils.Split(file, utils.PathSeparator, tokenizer)

			if tokenizer.Tokens[tokenizer.Counts-2] == partitions[k] {

				count++

				assertions.False(partitionFlushes[k])

				queuedFiles = append(queuedFiles, file)
			}
		}

		files[file] = struct{}{}
	}

	assertions.Equal(120, len(files))

}

func TestDataReaderFileDistributionVersion6(t *testing.T) {

	_ = os.RemoveAll(utils.EventDir)

	utils.MaxCurrentDayEventFileQueueSize = 10

	utils.MaxPreviousDayEventFileQueueSize = 1

	reader := NewDataReader(make([]*VerticalWriter, utils.VerticalWriters), nil, nil, nil)

	reader.dir = utils.EventDir + utils.PathSeparator + utils.VerticalFormat

	reader.readers = make([]*bitmap.Bitmap, utils.VerticalWriters)

	for index := range reader.readers {

		reader.readers[index] = &bitmap.Bitmap{}
	}

	reader.tokenizer = &utils.Tokenizer{

		Tokens: make([]string, 50),
	}

	reader.getFiles()

	assertions := assert.New(t)

	for _, file := range reader.files {

		assertions.Empty(file)
	}

}

func TestDataReaderFileDistributionVersion7(t *testing.T) {

	_ = os.RemoveAll(utils.EventDir)

	_ = os.MkdirAll(utils.EventDir+utils.PathSeparator+utils.HorizontalFormat+utils.PathSeparator+"1", 0755)

	_ = os.MkdirAll(utils.EventDir+utils.PathSeparator+utils.HorizontalFormat+utils.PathSeparator+"11", 0755)

	_ = os.MkdirAll(utils.EventDir+utils.PathSeparator+utils.HorizontalFormat+utils.PathSeparator+"12", 0755)

	_ = os.MkdirAll(utils.EventDir+utils.PathSeparator+utils.HorizontalFormat+utils.PathSeparator+"2", 0755)

	_ = os.MkdirAll(utils.EventDir+utils.PathSeparator+utils.HorizontalFormat+utils.PathSeparator+"3", 0755)

	utils.MaxCurrentDayEventFileQueueSize = 2

	utils.MaxPreviousDayEventFileQueueSize = 1

	writer := 0

	for i := 0; i < 20; i++ {

		_ = os.WriteFile(utils.EventDir+utils.PathSeparator+utils.HorizontalFormat+utils.PathSeparator+"12"+utils.PathSeparator+INTToStringValue(i)+utils.SpecialSeparator+INTToStringValue(writer), []byte(""), 0666)

		writer++

		if writer == utils.HorizontalWriters {

			writer = 0
		}
	}

	writer = 0

	for i := 20; i < 40; i++ {

		_ = os.WriteFile(utils.EventDir+utils.PathSeparator+utils.HorizontalFormat+utils.PathSeparator+"11"+utils.PathSeparator+INTToStringValue(i)+utils.SpecialSeparator+INTToStringValue(writer), []byte(""), 0666)

		writer++

		if writer == utils.HorizontalWriters {

			writer = 0
		}
	}

	writer = 0

	for i := 40; i < 45; i++ {

		_ = os.WriteFile(utils.EventDir+utils.PathSeparator+utils.HorizontalFormat+utils.PathSeparator+"3"+utils.PathSeparator+INTToStringValue(i)+utils.SpecialSeparator+INTToStringValue(writer), []byte(""), 0666)

		writer++

		if writer == utils.HorizontalWriters {

			writer = 0
		}
	}

	writer = 0

	for i := 45; i < 70; i++ {

		_ = os.WriteFile(utils.EventDir+utils.PathSeparator+utils.HorizontalFormat+utils.PathSeparator+"2"+utils.PathSeparator+INTToStringValue(i)+utils.SpecialSeparator+INTToStringValue(writer), []byte(""), 0666)

		writer++

		if writer == utils.HorizontalWriters {

			writer = 0
		}
	}

	writer = 0

	for i := 70; i < 120; i++ {

		_ = os.WriteFile(utils.EventDir+utils.PathSeparator+utils.HorizontalFormat+utils.PathSeparator+"1"+utils.PathSeparator+INTToStringValue(i)+utils.SpecialSeparator+INTToStringValue(writer), []byte(""), 0666)

		writer++

		if writer == utils.HorizontalWriters {

			writer = 0
		}
	}

	reader := NewDataReader(nil, make([]*HorizontalWriter, utils.HorizontalWriters), nil, nil)

	reader.dir = utils.EventDir + utils.PathSeparator + utils.HorizontalFormat

	reader.readers = make([]*bitmap.Bitmap, utils.HorizontalWriters)

	for index := range reader.readers {

		reader.readers[index] = &bitmap.Bitmap{}
	}

	reader.tokenizer = &utils.Tokenizer{

		Tokens: make([]string, 50),
	}

	reader.getFiles()

	assertions := assert.New(t)

	files := map[string]struct{}{}

	count := 0

	k := 0

	var queuedFiles []string

	counts := []int{2, 1, 1, 1, 1} //according to 3 partitions

	partitions := []string{"12", "11", "3", "2", "1"} //according to 3 partitions

	partitionFlushes := []bool{false, false, false, false, false}

	for _, file := range reader.files {

		flushed := true

		for _, partitionFlush := range partitionFlushes {

			if !partitionFlush {

				flushed = false

				break
			}

		}

		if flushed {

			break
		}

		utils.Split(file, utils.PathSeparator, tokenizer)

		if file != utils.Empty && (partitionFlushes[k] || tokenizer.Tokens[tokenizer.Counts-2] == partitions[k]) {

			count++

			if tokenizer.Tokens[tokenizer.Counts-2] == partitions[k] {

				assertions.False(partitionFlushes[k])

			}

			queuedFiles = append(queuedFiles, file)

		} else if file == utils.Empty {

			if counts[k] == count {

				assertions.True(sort.StringsAreSorted(queuedFiles))

				queuedFiles = nil

				k++

				if k == len(counts) {

					k = 0
				}

				count = 0
			}

			count++

			partitionFlushes[k] = true

			continue

		} else {

			assertions.Equal(counts[k], count)

			assertions.True(sort.StringsAreSorted(queuedFiles))

			queuedFiles = nil

			k++

			if k == len(counts) {

				k = 0
			}

			count = 0

			utils.Split(file, utils.PathSeparator, tokenizer)

			if tokenizer.Tokens[tokenizer.Counts-2] == partitions[k] {

				count++

				assertions.False(partitionFlushes[k])

				queuedFiles = append(queuedFiles, file)
			}
		}

		files[file] = struct{}{}
	}

	assertions.Equal(120, len(files))

}

func TestHorizontalWriterV1(t *testing.T) {

	assertions := assert.New(t)

	horizontalWriter := NewHorizontalWriter(25)

	horizontalWriter.Start()

	horizontalWriter.Events <- utils.MotadataMap{
		utils.File:              "dummy file name",
		datastore.DatastoreType: utils.None,
		utils.Tick:              1234,
		utils.Plugin:            "dummy plugin",
	}

	bytes := make([]byte, 5)

	binary.LittleEndian.PutUint32(bytes, 999)

	horizontalWriter.Events <- utils.MotadataMap{
		utils.Buffer:            bytes,
		datastore.DatastoreType: utils.StatusFlapHistory,
		utils.Tick:              1234,
		utils.Plugin:            "dummy plugin bytes",
	}

	horizontalWriter.Events <- utils.MotadataMap{
		utils.Buffer:            bytes,
		datastore.DatastoreType: utils.CorrelationMetric,
		utils.Tick:              1234,
		utils.Plugin:            "dummy plugin bytes 2",
	}

	time.Sleep(time.Second * 2)

	horizontalWriter.valueBytes = nil

	horizontalWriter.valueBufferBytes = nil

	horizontalWriter.ShutdownNotifications <- true

	horizontalWriter = NewHorizontalWriter(25)

	store, err := OpenOrCreateStore("dummy-store-name", utils.StaticMetric, encoder, tokenizer, false)

	assertions.Nil(err)

	store.MarkClosed(encoder)

	store, err = OpenOrCreateStore("dummy-store-name-2", utils.StaticMetric, encoder, tokenizer, false)

	assertions.Nil(err)

	horizontalWriter.dataStoreType = utils.Log

	horizontalWriter.compositeStore = store

	horizontalWriter.dataStoreType = utils.HealthMetric

	store.MarkClosed(encoder)

	horizontalWriter.tokenizer = &utils.Tokenizer{
		Tokens: make([]string, 30),
	}

	horizontalWriter.int32Fields["dd"] = make([]int32, 2)

	horizontalWriter.int32Fields["dd"+utils.MaxSuffix] = make([]int32, 2)

	horizontalWriter.write(store, 0, 1, 0, false)

	utils.AssertLogMessage(assertions, "Horizontal Writer", "writer", "occurred while writing event")

}

func TestIndexWriterV1(t *testing.T) {

	assertions := assert.New(t)

	configBytes, err := os.ReadFile(filepath.Dir(filepath.Dir(filepath.Dir(filepath.Dir(utils.CurrentDir)))) + utils.PathSeparator + utils.ConfigDir + utils.PathSeparator + utils.MotadataDatastoreConfigFile)

	assertions.Nil(err)

	utils.InitConfigs(utils.UpdateConfigs(configBytes, utils.MotadataMap{
		"datastore.memory.pool.shrink.timer.seconds": 1,
	}))

	indexWriter := NewIndexWriter(0)

	indexWriter.encoder.MemoryPool = nil

	indexWriter.Start()

	time.Sleep(time.Second * 1)

	indexWriter.valueBytes = nil

	indexWriter.ShutdownNotifications <- true

	time.Sleep(time.Second * 3)

	utils.AssertLogMessage(assertions, "Index Writer", "writer", "failed to unmap anonymous mapped buffer,reason")

}

func TestIndexWriterV2(t *testing.T) {

	assertions := assert.New(t)

	indexWriter := NewIndexWriter(0)

	indexWriter.Start()

	store, err := OpenOrCreateStore("dummy-index-writer-testing", utils.Index, encoder, tokenizer, false)

	assertions.Nil(err)

	assertions.NotNil(store)

	tick := time.Now().Unix()

	column := "dummy"

	plugin := "dummy-plugin"

	storeName := datastore.GetStoreName(int32(tick), column+utils.HyphenSeparator+plugin, datastore.HorizontalStore) + utils.HyphenSeparator + INTToStringValue(int(Int64))

	store, err = OpenOrCreateStore(storeName, utils.None, encoder, tokenizer, false)

	datastore.RemoveStore(storeName)

	store.Close(encoder)

	poolLength := utils.MaxPoolLength

	utils.MaxPoolLength = 100

	indexWriter.writeIndex(utils.IndexEvent{
		LookupEvent:    true,
		NumericContext: map[int]struct{}{},
		StringContext:  map[string]struct{}{},
		StoreType:      utils.None,
		Plugin:         plugin,
		Tick:           int32(tick),
		Column:         column,
	})

	utils.MaxPoolLength = poolLength

	storeName = datastore.GetStoreName(int32(tick), column+utils.HyphenSeparator+plugin, datastore.HorizontalStore) + utils.HyphenSeparator + INTToStringValue(int(String))

	store, err = OpenOrCreateStore(storeName, utils.None, encoder, tokenizer, false)

	datastore.RemoveStore(storeName)

	store.Close(encoder)

	utils.MaxPoolLength = 100

	indexWriter.writeIndex(utils.IndexEvent{
		LookupEvent:    true,
		NumericContext: nil,
		StringContext:  map[string]struct{}{},
		StoreType:      utils.None,
		Plugin:         plugin,
		Tick:           int32(tick),
		Column:         column,
	})

	column = "dummy-column2"

	storeName = datastore.GetStoreName(int32(tick), column+utils.HyphenSeparator+plugin, datastore.HorizontalStore) + utils.HyphenSeparator + INTToStringValue(int(Int64))

	store = datastore.GetStore(storeName, utils.None, true, true, encoder, tokenizer)

	store.Close(encoder)

	store.MarkClosed(encoder)

	indexWriter.writeIndex(utils.IndexEvent{
		LookupEvent: true,
		NumericContext: map[int]struct{}{
			5: {},
		},
		StringContext: map[string]struct{}{},
		StoreType:     utils.None,
		Plugin:        plugin,
		Tick:          int32(tick),
		Column:        column,
	})

	storeName = datastore.GetStoreName(int32(tick), column+utils.HyphenSeparator+plugin, datastore.HorizontalStore) + utils.HyphenSeparator + INTToStringValue(int(String))

	store = datastore.GetStore(storeName, utils.None, true, true, encoder, tokenizer)

	store.Close(encoder)

	store.MarkClosed(encoder)

	indexWriter.writeIndex(utils.IndexEvent{
		LookupEvent:    true,
		NumericContext: nil,
		StringContext: map[string]struct{}{
			"dummy-token": {},
		},
		StoreType: utils.None,
		Plugin:    plugin,
		Tick:      int32(tick),
		Column:    column,
	})

	datastore.AddSearchableColumn(column)

	indexWriter.writeIndex(utils.IndexEvent{
		LookupEvent:    true,
		NumericContext: nil,
		StringContext: map[string]struct{}{
			"dummyToken": {},
		},
		StoreType: utils.None,
		Plugin:    plugin,
		Tick:      int32(tick),
		Column:    column,
	})

	utils.MaxPoolLength = poolLength

	storeName = datastore.GetStoreName(int32(tick), utils.DummyPostingListStoreName, datastore.HorizontalStore) + utils.HyphenSeparator + INTToStringValue(int(String))

	store, err = OpenOrCreateStore(storeName, utils.None, encoder, tokenizer, false)

	datastore.RemoveStore(storeName)

	store.Close(encoder)

	utils.MaxPoolLength = 100

	indexWriter.writeIndex(utils.IndexEvent{
		LookupEvent:    false,
		NumericContext: nil,
		StringContext:  nil,
		StoreType:      utils.None,
		Plugin:         plugin,
		Tick:           int32(tick),
		Column:         column,
	})

	plugin = "dummyPlugin2"

	utils.MaxPoolLength = poolLength

	storeName = datastore.GetStoreName(int32(tick), utils.DummyPostingListStoreName, datastore.HorizontalStore) + utils.HyphenSeparator + INTToStringValue(int(String))

	store = datastore.GetStore(storeName, utils.None, true, true, encoder, tokenizer)

	store.Close(encoder)

	store.MarkClosed(encoder)

	indexWriter.writeIndex(utils.IndexEvent{
		LookupEvent:    false,
		NumericContext: nil,
		StringContext:  nil,
		StoreType:      utils.None,
		Plugin:         plugin,
		Tick:           int32(tick),
		Column:         column,
	})

	indexWriter.writeIndex(utils.IndexEvent{
		LookupEvent:    true,
		NumericContext: nil,
		StringContext:  nil,
		StoreType:      utils.CorrelationMetric,
		Plugin:         plugin,
		Tick:           int32(tick),
		Column:         column,
	})

	indexWriter.writeIndex(utils.IndexEvent{
		LookupEvent:    true,
		NumericContext: nil,
		StringContext:  nil,
		StoreType:      utils.None,
		Plugin:         plugin,
		Tick:           int32(tick),
		Column:         column,
	})

	utils.AssertLogMessage(assertions, "Index Writer", "writer", "failed to update the posting list index")
}

func TestWriteTxn(t *testing.T) {

	horizontalWriter1 := NewHorizontalWriter(1)

	valueBytes := []byte("12345678dummy-value-bytes")

	field := "dummy.metric"

	assertions := assert.New(t)

	var err error

	for i := 0; i < 1000000; i++ {

		err = horizontalWriter1.writeTxn(field, 0, valueBytes, true)

		assertions.Nil(err)
	}

	keyBytes := []byte(datastore.GetKey(horizontalWriter1.tick, field, 0))

	store, err := OpenOrCreateStore("dummy-store-type-1", utils.None, encoder, tokenizer, false)

	assertions.Nil(err)

	copy(horizontalWriter1.txnBufferBytes[horizontalWriter1.txnOffset:], utils.EOTBytes)

	horizontalWriter1.txnOffset += len(utils.EOTBytes)

	WriteINT32Value(int32(horizontalWriter1.txnOffset-4), 0, horizontalWriter1.txnBufferBytes)

	err = horizontalWriter1.commitTxn(store, false, horizontalWriter1.txnBufferBytes, horizontalWriter1.txnEntries, 1)

	valueBytes = make([]byte, 1000)

	wg := sync.WaitGroup{}

	err = store.Sync(encoder)

	assertions.Nil(err)

	found, data, err := store.Get(keyBytes, valueBytes, encoder, DiskIOEvent{}, &wg, tokenizer, false)

	assertions.Nil(err)

	assertions.True(found)

	assertions.Equal(data, []byte("dummy-value-bytes"))

	utils.AssertLogMessage(assertions, "Horizontal Writer", "writer", "remapping annonymous txn buffers with current length")

	utils.AssertLogMessage(assertions, "Horizontal Writer", "writer", "remapping annonymous txn buffers with current length")

}

func TestTrapCorruptedRecord(t *testing.T) {

	writer := NewVerticalWriter(1, []*StaticMetricWriter{staticMetricWriter})

	writer.overflowPoolLength = 10000

	writer.shutdown = true

	writer.Start()

	assertions := assert.New(t)

	ticks := []int32{utils.UnixToSeconds(1663146799), utils.UnixToSeconds(1663146800)}

	values := []string{"value1"}

	for key := range writer.verticalCacheEntries {

		delete(writer.verticalCacheEntries, key)
	}

	plugin := "2101-trap"

	tick := int64(1663146799)

	storeName := datastore.GetStoreById(tick, plugin+".flap", datastore.VerticalStore)

	store := datastore.GetStore(storeName, utils.TrapFlapHistory, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	part := uint16(0)

	eventSource := "127.0.0.1"

	oid := "1.2.3.4"

	field := "metric"

	key := eventSource + utils.KeySeparator + oid + utils.KeySeparator + field

	timeKeyBytes := []byte(key + utils.KeySeparator + datastore.Time + utils.KeySeparator + UINT16ToStringValue(part))

	valueKeyBytes := []byte(key + utils.KeySeparator + UINT16ToStringValue(part))

	bufferIndex, bufferBytes := encoder.WriteUINT32Value(uint32(len(ticks)), 0)

	poolIndex, valueBytes := encoder.EncodeRLEDeltaINT32Values(ticks, ticks[0], len(bufferBytes)+utils.MaxValueBytes)

	copy(valueBytes[utils.MaxValueBytes:], bufferBytes)

	err := store.Put(timeKeyBytes, valueBytes, encoder, tokenizer)

	assertions.Nil(err)

	encoder.MemoryPool.ReleaseBytePool(bufferIndex)

	encoder.MemoryPool.ReleaseBytePool(poolIndex)

	bufferIndex, bufferBytes, err = encoder.EncodeStringValues(values, None, utils.MaxValueBytes, string(valueKeyBytes))

	assertions.Nil(err)

	err = store.Put(valueKeyBytes, bufferBytes, encoder, tokenizer)

	assertions.Nil(err)

	encoder.MemoryPool.ReleaseBytePool(bufferIndex)

	tick = 1663146800

	columns := []string{utils.TrapOID, field, utils.TrapEnterpriseId, utils.TrapMessage, utils.TrapName, utils.TrapSeverity}

	values2 := []interface{}{oid, "value3", "0.0.1", "message1", "trap1", "up"}

	dataTypes := []byte{datastore.StringColumn, datastore.StringColumn, datastore.StringColumn, datastore.StringColumn, datastore.StringColumn, datastore.StringColumn}

	rowBuffer := &bytes2.Buffer{}

	for index := range columns {

		PackRowBuffer(columns[index], ToString(values2[index]), dataTypes[index], rowBuffer)
	}

	batch := PackEventBatchV3(plugin, tick, eventSource, utils.TrapFlapHistory, rowBuffer)

	atomic.AddInt32(&writer.Pending, 1)

	writer.bufferBytes = batch[utils.Buffer].([]uint8)

	writer.writeTrap(batch)

	writer.flushCaches(true)

	store.Sync(encoder)

	bytes := make([]byte, 100000)

	wg := sync.WaitGroup{}

	found, buffBytes, err := store.Get(timeKeyBytes, bytes, encoder, DiskIOEvent{}, &wg, tokenizer, false)

	assertions.Nil(err)

	assertions.True(found)

	tickPoolIndex, decodedTicks, err := datastore.GetTimeTicks(buffBytes, 0, writer.decoder)

	assertions.Nil(err)

	assertions.Equal(decodedTicks, []int32{utils.UnixToSeconds(1663146799), utils.UnixToSeconds(1663146800)})

	writer.decoder.MemoryPool.ReleaseINT32Pool(tickPoolIndex)

	found, buffBytes, err = store.Get(valueKeyBytes, bytes, encoder, DiskIOEvent{}, &wg, tokenizer, false)

	assertions.Nil(err)

	assertions.True(found)

	valuePoolIndex, decodedValues, err := writer.decoder.DecodeStringValues(GetEncoding(buffBytes[0]), buffBytes[1:], string(writer.keyBuffers[0]), store.GetName(), 0)

	assertions.Nil(err)

	assertions.Equal(decodedValues, []string{"value1", "value3"})

	writer.decoder.MemoryPool.ReleaseStringPool(valuePoolIndex)
}

func TestTrapCorruptedRecordV2(t *testing.T) {

	writer := NewVerticalWriter(1, []*StaticMetricWriter{staticMetricWriter})

	writer.overflowPoolLength = 10000

	writer.shutdown = true

	writer.Start()

	assertions := assert.New(t)

	ticks := []int32{utils.UnixToSeconds(1663146799), utils.UnixToSeconds(1663146800)}

	values := []int32{15}

	for key := range writer.verticalCacheEntries {

		delete(writer.verticalCacheEntries, key)
	}

	plugin := "109-dummy"

	tick := int64(1663146801)

	storeName := datastore.GetStoreById(tick, plugin, datastore.VerticalStore)

	store := datastore.GetStore(storeName, utils.PerformanceMetric, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	part := uint16(0)

	monitorId := "1"

	metric := "metric"

	instance := "dummy"

	key := monitorId + utils.KeySeparator + instance + utils.KeySeparator + metric

	timeKeyBytes := []byte(key + utils.KeySeparator + datastore.Time + utils.KeySeparator + UINT16ToStringValue(part))

	valueKeyBytes := []byte(key + utils.KeySeparator + UINT16ToStringValue(part))

	bufferIndex, bufferBytes := encoder.WriteUINT32Value(uint32(len(ticks)), 0)

	poolIndex, valueBytes := encoder.EncodeRLEDeltaINT32Values(ticks, ticks[0], len(bufferBytes)+utils.MaxValueBytes)

	copy(valueBytes[utils.MaxValueBytes:], bufferBytes)

	err := store.Put(timeKeyBytes, valueBytes, encoder, tokenizer)

	assertions.Nil(err)

	encoder.MemoryPool.ReleaseBytePool(bufferIndex)

	encoder.MemoryPool.ReleaseBytePool(poolIndex)

	currentDataType := Int32

	bufferIndex, bufferBytes, err = encoder.EncodeINT32Values(values, None, currentDataType, GetDataTypeBits(currentDataType), utils.MaxValueBytes)

	assertions.Nil(err)

	err = store.Put(valueKeyBytes, bufferBytes, encoder, tokenizer)

	assertions.Nil(err)

	encoder.MemoryPool.ReleaseBytePool(bufferIndex)

	//inserting int value

	writer.tick = time.UnixMilli(utils.SecondsToUnixMillis(utils.UnixToSeconds(tick))).Unix()

	err = writer.writeMetricCacheEntries(key, metric, storeName, int64(20), monitorId, Int32, false)

	assertions.Nil(err)

	writer.flushCaches(false)

	bytes := make([]byte, 100000)

	wg := sync.WaitGroup{}

	found, buffBytes, err := store.Get(timeKeyBytes, bytes, encoder, DiskIOEvent{}, &wg, tokenizer, false)

	assertions.Nil(err)

	assertions.True(found)

	tickPoolIndex, decodedTicks, err := datastore.GetTimeTicks(buffBytes, 0, writer.decoder)

	assertions.Nil(err)

	assertions.Equal(decodedTicks, []int32{utils.UnixToSeconds(1663146799), utils.UnixToSeconds(1663146801)})

	writer.decoder.MemoryPool.ReleaseINT32Pool(tickPoolIndex)

	found, buffBytes, err = store.Get(valueKeyBytes, bytes, encoder, DiskIOEvent{}, &wg, tokenizer, false)

	assertions.Nil(err)

	assertions.True(found)

	valuePoolIndex, decodedValues, err := writer.decoder.DecodeINT32Values(GetEncoding(buffBytes[0]), Int32, buffBytes[1:], string(writer.keyBuffers[0]), store.GetName(), 0)

	assertions.Nil(err)

	assertions.Equal(decodedValues, []int32{15, 20})

	writer.decoder.MemoryPool.ReleaseINT32Pool(valuePoolIndex)

}

func TestTrapCorruptedBlobRecordV1(t *testing.T) {

	writer := NewVerticalWriter(1, []*StaticMetricWriter{staticMetricWriter})

	writer.overflowPoolLength = 10000

	writer.shutdown = true

	writer.Start()

	assertions := assert.New(t)

	ticks := []int32{utils.UnixToSeconds(1663146799), utils.UnixToSeconds(1663146800)}

	values := []string{"value1"}

	for key := range writer.verticalCacheEntries {

		delete(writer.verticalCacheEntries, key)
	}

	plugin := "2102-trap"

	tick := int64(1663146801)

	storeName := datastore.GetStoreById(tick, plugin+".flap", datastore.VerticalStore)

	store := datastore.GetStore(storeName, utils.TrapFlapHistory, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	part := uint16(0)

	eventSource := "127.0.0.1"

	oid := "1.2.3.4"

	field := "metric"

	datastore.AddBlobColumn(field)

	key := eventSource + utils.KeySeparator + oid + utils.KeySeparator + field

	timeKeyBytes := []byte(key + utils.KeySeparator + datastore.Time + utils.KeySeparator + UINT16ToStringValue(part))

	keyBytes := []byte(key + utils.KeySeparator + UINT16ToStringValue(part))

	bufferIndex, bufferBytes := encoder.WriteUINT32Value(uint32(len(ticks)), 0)

	poolIndex, valueBytes := encoder.EncodeRLEDeltaINT32Values(ticks, ticks[0], len(bufferBytes)+utils.MaxValueBytes)

	copy(valueBytes[utils.MaxValueBytes:], bufferBytes)

	err := store.Put(timeKeyBytes, valueBytes, encoder, tokenizer)

	assertions.Nil(err)

	encoder.MemoryPool.ReleaseBytePool(bufferIndex)

	encoder.MemoryPool.ReleaseBytePool(poolIndex)

	bytes := make([]byte, 100000)

	wg := sync.WaitGroup{}

	blobEvent := datastore.BlobEvent{}

	blobEvent.KeyBytes = keyBytes

	blobEvent.ValueBytes = bytes

	blobEvent.Values = values

	blobEvent.Encoder = encoder

	blobEvent.Store = store

	blobEvent.Padding = utils.MaxValueBytes

	blobEvent.Tokenizer = tokenizer

	blobEvent.DiskIOEvent = DiskIOEvent{}

	blobEvent.WaitGroup = &wg

	blobEvent.Encoding = None

	headerIndex, headerBytes, err := datastore.WriteBlobColumnValues(blobEvent)

	assertions.Nil(err)

	err = store.Put(keyBytes, headerBytes, encoder, tokenizer)

	encoder.MemoryPool.ReleaseBytePool(headerIndex)

	tick = 1663146801

	columns := []string{utils.TrapOID, field, utils.TrapEnterpriseId, utils.TrapMessage, utils.TrapName, utils.TrapSeverity}

	values2 := []interface{}{oid, "value3", "0.0.1", "message1", "trap1", "up"}

	dataTypes := []byte{datastore.StringColumn, datastore.StringColumn, datastore.StringColumn, datastore.StringColumn, datastore.StringColumn, datastore.StringColumn}

	rowBuffer := &bytes2.Buffer{}

	for index := range columns {

		PackRowBuffer(columns[index], ToString(values2[index]), dataTypes[index], rowBuffer)
	}

	batch := PackEventBatchV3(plugin, tick, eventSource, utils.TrapFlapHistory, rowBuffer)

	atomic.AddInt32(&writer.Pending, 1)

	writer.bufferBytes = batch[utils.Buffer].([]uint8)

	writer.writeTrap(batch)

	writer.flushCaches(true)

	store.Sync(encoder)

	valueBufferBytes := make([]byte, 100000)

	wg = sync.WaitGroup{}

	found, buffBytes, err := store.Get(timeKeyBytes, valueBufferBytes, encoder, DiskIOEvent{}, &wg, tokenizer, false)

	assertions.Nil(err)

	assertions.True(found)

	tickPoolIndex, decodedTicks, err := datastore.GetTimeTicks(buffBytes, 0, writer.decoder)

	assertions.Nil(err)

	assertions.Equal(decodedTicks, []int32{utils.UnixToSeconds(1663146799), utils.UnixToSeconds(1663146801)})

	writer.decoder.MemoryPool.ReleaseINT32Pool(tickPoolIndex)

	found, buffBytes, err = store.Get(keyBytes, valueBufferBytes, encoder, DiskIOEvent{}, &wg, tokenizer, true)

	values = []string{}

	blobEvent.KeyBytes = keyBytes

	blobEvent.Encoder = encoder

	blobEvent.Decoder = decoder

	blobEvent.Store = store

	blobEvent.Tokenizer = tokenizer

	blobEvent.DiskIOEvent = DiskIOEvent{}

	blobEvent.WaitGroup = &wg

	blobEvent.Encoding = GetEncoding(bufferBytes[0])

	for i := 1; i < len(buffBytes); i += 13 {

		blobEvent.ValueBytes = buffBytes[i:]

		blobEvent.Encoding = GetEncoding(buffBytes[0])

		index, blobValues, err := datastore.GetBlobColumnValues(blobEvent)

		assertions.Nil(err)

		values = append(values, blobValues...)

		writer.decoder.MemoryPool.ReleaseStringPool(index)
	}

	assertions.Equal(values, []string{"value1", "value3"})

}

func TestCorruptedBlobRecordV2(t *testing.T) {

	writer := NewVerticalWriter(1, []*StaticMetricWriter{staticMetricWriter})

	writer.overflowPoolLength = 10000

	writer.shutdown = true

	writer.Start()

	assertions := assert.New(t)

	ticks := []int32{utils.UnixToSeconds(1663146799), utils.UnixToSeconds(1663146800)}

	values := []string{"value1"}

	for key := range writer.verticalCacheEntries {

		delete(writer.verticalCacheEntries, key)
	}

	plugin := "2103-trap"

	tick := int64(1663146801)

	storeName := datastore.GetStoreById(tick, plugin+".flap", datastore.VerticalStore)

	store := datastore.GetStore(storeName, utils.TrapFlapHistory, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	part := uint16(0)

	eventSource := "127.0.0.1"

	oid := "1.2.3.4"

	field := "metric"

	datastore.AddBlobColumn(field)

	key := eventSource + utils.KeySeparator + oid + utils.KeySeparator + field

	timeKeyBytes := []byte(key + utils.KeySeparator + datastore.Time + utils.KeySeparator + UINT16ToStringValue(part))

	valueKeyBytes := []byte(key + utils.KeySeparator + UINT16ToStringValue(part))

	bufferIndex, bufferBytes := encoder.WriteUINT32Value(uint32(len(ticks)), 0)

	poolIndex, valueBytes := encoder.EncodeRLEDeltaINT32Values(ticks, ticks[0], len(bufferBytes)+utils.MaxValueBytes)

	copy(valueBytes[utils.MaxValueBytes:], bufferBytes)

	err := store.Put(timeKeyBytes, valueBytes, encoder, tokenizer)

	assertions.Nil(err)

	encoder.MemoryPool.ReleaseBytePool(bufferIndex)

	encoder.MemoryPool.ReleaseBytePool(poolIndex)

	wg := sync.WaitGroup{}

	tick = 1663146801

	columns := []string{utils.TrapOID, field, utils.TrapEnterpriseId, utils.TrapMessage, utils.TrapName, utils.TrapSeverity}

	values2 := []interface{}{oid, "value3", "0.0.1", "message1", "trap1", "up"}

	dataTypes := []byte{datastore.StringColumn, datastore.StringColumn, datastore.StringColumn, datastore.StringColumn, datastore.StringColumn, datastore.StringColumn}

	rowBuffer := &bytes2.Buffer{}

	for index := range columns {

		PackRowBuffer(columns[index], ToString(values2[index]), dataTypes[index], rowBuffer)
	}

	batch := PackEventBatchV3(plugin, tick, eventSource, utils.TrapFlapHistory, rowBuffer)

	atomic.AddInt32(&writer.Pending, 1)

	writer.bufferBytes = batch[utils.Buffer].([]uint8)

	writer.writeTrap(batch)

	writer.flushCaches(true)

	store.Sync(encoder)

	bytes := make([]byte, 100000)

	wg = sync.WaitGroup{}

	found, buffBytes, err := store.Get(timeKeyBytes, bytes, encoder, DiskIOEvent{}, &wg, tokenizer, false)

	assertions.Nil(err)

	assertions.True(found)

	tickPoolIndex, decodedTicks, err := datastore.GetTimeTicks(buffBytes, 0, writer.decoder)

	assertions.Nil(err)

	assertions.Equal(decodedTicks, []int32{utils.UnixToSeconds(1663146801)})

	writer.decoder.MemoryPool.ReleaseINT32Pool(tickPoolIndex)

	found, buffBytes, err = store.Get(valueKeyBytes, bytes, encoder, DiskIOEvent{}, &wg, tokenizer, true)

	values = []string{}

	blobEvent := datastore.BlobEvent{}

	blobEvent.KeyBytes = valueKeyBytes

	blobEvent.Encoder = encoder

	blobEvent.Decoder = decoder

	blobEvent.Store = store

	blobEvent.Tokenizer = tokenizer

	blobEvent.DiskIOEvent = DiskIOEvent{}

	blobEvent.WaitGroup = &wg

	blobEvent.Encoding = GetEncoding(bufferBytes[0])

	for i := 1; i < len(buffBytes); i += 13 {

		blobEvent.ValueBytes = buffBytes[i:]

		blobEvent.Encoding = GetEncoding(buffBytes[0])

		index, blobValues, err := datastore.GetBlobColumnValues(blobEvent)

		assertions.Nil(err)

		values = append(values, blobValues...)

		writer.decoder.MemoryPool.ReleaseStringPool(index)
	}

	assertions.Equal(values, []string{"value3"})

}

func TestIndexStoreType(t *testing.T) {

	assertions := assert.New(t)

	assertions.Equal(getIndexStoreType(utils.PolicyAggregation), utils.PolicyIndex)

	assertions.Equal(getIndexStoreType(utils.TrapAggregation), utils.TrapIndex)

	assertions.Equal(getIndexStoreType(utils.LogAggregation), utils.LogIndex)

	assertions.Equal(getIndexStoreType(utils.FlowAggregation), utils.FlowIndex)
}

func TestValidateRegexURL(t *testing.T) {

	assertions := assert.New(t)

	value := "https://www.motadata.com"

	assertions.True(regex.MatchString(value))

	value = "www.motadata.com"

	assertions.True(regex.MatchString(value))

	value = "https://*************/inventory/Server/monitors/54825099143"

	assertions.True(regex.MatchString(value))

	value = "https://www.youtube.com/watch?v=X3jw1JVNdPE&t=298s&ab_channel=Fireship"

	assertions.True(regex.MatchString(value))

	value = "https://www.google.com/search?q=motadata&oq=motadata+&gs_lcrp=EgZjaHJvbWUqBggAEEUYOzIGCAAQRRg7MgcIARAAGIAEMgcIAhAAGIAEMg0IAxAuGK8BGMcBGIAEMgYIBBBFGDwyBggFEEUYPDIGCAYQRRhBMgYIBxBFGDzSAQgxOTYxajBqN6gCALACAA&sourceid=chrome&ie=UTF-8"

	assertions.Greater(len(value), 100)

	assertions.True(regex.MatchString(value))

}

func TestInvalidIndexableColumn(t *testing.T) {

	writer := NewHorizontalWriter(25)

	writer.shutdown = true

	writer.Start()

	utils.CleanUpStores()

	assertions := assert.New(t)

	plugin := "dummy.testing.non.indexable"

	invalidIndexableColumn := "invalid.indexable.column"

	indexableColumn := "indexable.column"

	url := "url"

	maxBlobBytes := utils.MaxBlobBytes

	defer func() {

		utils.MaxBlobBytes = maxBlobBytes
	}()

	utils.MaxBlobBytes = 100

	utils.IndexJobRequests = make(chan utils.MotadataMap, 100)

	columns := []string{indexableColumn, invalidIndexableColumn, url}

	values := []interface{}{utils.GenerateRandomString(49), utils.GenerateRandomString(101), "https://www.google.com/search?q=motadata&oq=motadata+&gs_lcrp=EgZjaHJvbWUqBggAEEUYOzIGCAAQRRg7MgcIARAAGIAEMgcIAhAAGIAEMg0IAxAuGK8BGMcBGIAEMgYIBBBFGDwyBggFEEUYPDIGCAYQRRhBMgYIBxBFGDzSAQgxOTYxajBqN6gCALACAA&sourceid=chrome&ie=UTF-8"}

	dataTypes := []byte{datastore.StringColumn, datastore.StringColumn, datastore.StringColumn}

	rowBuffer := &bytes2.Buffer{}

	for index := range columns {

		PackRowBuffer(columns[index], ToString(values[index]), dataTypes[index], rowBuffer)

	}

	batch := PackEventBatchV3(plugin, time.Now().Unix(), "************", utils.Log, rowBuffer)

	atomic.AddInt32(&horizontalWriter.Pending, 1)

	writer.writeHorizontalBatch(batch)

	assertions.True(datastore.IsInvalidIndexableColumn(plugin, invalidIndexableColumn))

}

func TestInvalidIndexableColumnProbeRestriction(t *testing.T) {

	writer := NewHorizontalWriter(25)

	writer.shutdown = true

	writer.Start()

	utils.CleanUpStores()

	assertions := assert.New(t)

	plugin := "dummy.testing.non.indexable.1"

	invalidIndexableColumn := "invalid.indexable.column"

	indexableColumn := "indexable.column"

	url := "url"

	utils.IndexJobRequests = make(chan utils.MotadataMap, 100)

	columns := []string{indexableColumn, invalidIndexableColumn, url}

	values := []interface{}{utils.GenerateRandomString(49), utils.GenerateRandomString(101), "https://www.google.com/search?q=motadata&oq=motadata+&gs_lcrp=EgZjaHJvbWUqBggAEEUYOzIGCAAQRRg7MgcIARAAGIAEMgcIAhAAGIAEMg0IAxAuGK8BGMcBGIAEMgYIBBBFGDwyBggFEEUYPDIGCAYQRRhBMgYIBxBFGDzSAQgxOTYxajBqN6gCALACAA&sourceid=chrome&ie=UTF-8"}

	dataTypes := []byte{datastore.StringColumn, datastore.StringColumn, datastore.StringColumn}

	rowBuffer := &bytes2.Buffer{}

	for index := range columns {

		PackRowBuffer(columns[index], ToString(values[index]), dataTypes[index], rowBuffer)

	}

	batch := PackEventBatchV3(plugin, time.Now().Unix(), "************", utils.Log, rowBuffer)

	atomic.AddInt32(&horizontalWriter.Pending, 1)

	writer.writeHorizontalBatch(batch)

	assertions.True(datastore.IsInvalidIndexableColumn(plugin, invalidIndexableColumn))

}

// after 1 lakh probing there might be a scenario in which data length is big in size , hence need to skip such records.
func TestInvalidIndexableColumnProbeRestrictionType1(t *testing.T) {

	writer := NewHorizontalWriter(25)

	writer.shutdown = true

	writer.Start()

	utils.CleanUpStores()

	assertions := assert.New(t)

	plugin := "dummy.testing.non.indexable.2"

	invalidIndexableColumn := "invalid.indexable.column"

	indexableColumn := "indexable.column"

	url := "url"

	indexableFields := make(map[string]interface{})

	indexableFields[indexableColumn] = struct{}{}

	indexableFields[invalidIndexableColumn] = struct{}{}

	indexableFields[url] = struct{}{}

	datastore.AlterIndexableColumns(plugin, indexableFields, utils.Add)

	assertions.True(datastore.IsIndexableColumn(plugin, invalidIndexableColumn))

	utils.IndexJobRequests = make(chan utils.MotadataMap, 100)

	columns := []string{indexableColumn, invalidIndexableColumn, url}

	values := []interface{}{utils.GenerateRandomString(49), utils.GenerateRandomString(101), "https://www.google.com/search?q=motadata&oq=motadata+&gs_lcrp=EgZjaHJvbWUqBggAEEUYOzIGCAAQRRg7MgcIARAAGIAEMgcIAhAAGIAEMg0IAxAuGK8BGMcBGIAEMgYIBBBFGDwyBggFEEUYPDIGCAYQRRhBMgYIBxBFGDzSAQgxOTYxajBqN6gCALACAA&sourceid=chrome&ie=UTF-8"}

	dataTypes := []byte{datastore.StringColumn, datastore.StringColumn, datastore.StringColumn}

	rowBuffer := &bytes2.Buffer{}

	for index := range columns {

		PackRowBuffer(columns[index], ToString(values[index]), dataTypes[index], rowBuffer)

	}

	batch := PackEventBatchV3(plugin, time.Now().Unix(), "************", utils.Log, rowBuffer)

	atomic.AddInt32(&horizontalWriter.Pending, 1)

	writer.writeHorizontalBatch(batch)

	assertions.False(datastore.IsIndexableColumn(plugin, invalidIndexableColumn))

}

func TestGenerateRandomValueV1(t *testing.T) {

	assertions := assert.New(t)

	assertions.NotNil(generateRandomValueV1(Float16))

	assertions.NotNil(generateRandomValueV1(String))
}

func TestVerticalWriterV1(t *testing.T) {

	assertions := assert.New(t)

	writerValueBufferBytes := utils.GetDataWriterValueBufferBytes()

	maxValueBufferBytes := utils.MaxValueBufferBytes

	defer func() {

		utils.DataWriterValueBufferBytes = writerValueBufferBytes

		utils.MaxValueBufferBytes = maxValueBufferBytes
	}()

	utils.DataWriterValueBufferBytes = 0

	utils.MaxValueBufferBytes = 0

	writer := NewVerticalWriter(0, nil)

	writer.Start()

	time.Sleep(time.Millisecond * 100)

	writer.ShutdownNotifications <- true

	plugin := "101-performance"

	columns := []string{"column1", "column2", "column3", "column4"}

	values := []interface{}{"abc", 1, 3, 1.45}

	dataTypes := []byte{datastore.StringColumn, datastore.IntegerColumn, datastore.IntegerColumn, datastore.FloatingColumn}

	objectId := int32(1)

	instance := "x"

	writer.bufferBytes = packMetricBatchV4(columns, values, dataTypes, objectId, instance)

	writer.bufferBytes[0] = 10

	writer.bufferBytes[1] = 10

	writer.bufferBytes[2] = 10

	writer.bufferBytes[3] = 10

	writer.writePerformanceMetric(utils.MotadataMap{
		utils.Tick:   time.Now().Unix(),
		utils.Plugin: plugin,
	})

	writer.bufferBytes = packMetricBatchV4(columns, values, dataTypes, objectId, instance)

	utils.GlobalShutdown = true

	writer.position = 0

	writer.processPerformanceBatch(len(writer.bufferBytes), 0)

	utils.GlobalShutdown = false

	writer.position = 0

	store := datastore.GetStore(datastore.Object+utils.HyphenSeparator+datastore.Mappings, utils.Mapping, true, true, writer.encoder, writer.tokenizers[1])

	store.Close(encoder)

	writer.tick = time.Now().Unix()

	datastore.RemoveStore(datastore.GetStoreById(writer.tick, plugin, datastore.VerticalStore))

	store = datastore.GetStore(datastore.GetStoreById(writer.tick, writer.plugin, datastore.VerticalStore), utils.PerformanceMetric, true, true, writer.encoder, writer.tokenizers[1])

	assertions.NotNil(store)

	poolLength := utils.MaxPoolLength

	defer func() {

		utils.MaxPoolLength = poolLength
	}()

	utils.MaxPoolLength = 100

	store.Close(encoder)

	datastore.RemoveStore(store.GetName())

	writer.processPerformanceBatch(len(writer.bufferBytes), 0)

}

func TestVerticalWriterV2(t *testing.T) {

	assertions := assert.New(t)

	writer := NewVerticalWriter(1, nil)

	writer.Start()

	plugin := "101-performance.2"

	columns := []string{"column1", "column2", "column3", "column4"}

	values := []interface{}{"abc", 1, 3, 1.45}

	dataTypes := []byte{datastore.StringColumn, datastore.IntegerColumn, datastore.IntegerColumn, datastore.FloatingColumn}

	objectId := int32(1)

	instance := "x"

	writer.bufferBytes = packMetricBatchV4(columns, values, dataTypes, objectId, instance)

	writer.position = 0

	writer.plugin = plugin

	writer.processPerformanceBatch(len(writer.bufferBytes), 0)

	writer.position = 0

	writer.plugin = plugin

	datastore.AddGarbageColumn("column2")

	store := datastore.GetStore(datastore.Object+utils.HyphenSeparator+datastore.Mappings, utils.Mapping, true, true, writer.encoder, writer.tokenizers[1])

	store.Close(encoder)

	writer.processPerformanceBatch(len(writer.bufferBytes), 0)

	datastore.RemoveStore(store.GetName())

	writer.position = 0

	writer.bufferBytes = []byte{0, 0, 0, 0, 0, 0, 0}

	writer.writeStatusMetric(utils.MotadataMap{
		utils.Tick:   time.Now().Unix(),
		utils.Plugin: plugin,
	})

	plugin = "dummy.plugin"

	stringValues := []string{"1", "instance", "uptime", "", "up~uptime", "1234"}

	writer.tick = time.Now().Unix()

	writer.plugin = plugin

	store = datastore.GetStore(datastore.GetStoreById(writer.tick, writer.plugin, datastore.VerticalStore), utils.ObjectStatusMetric, true, true, writer.encoder, writer.tokenizers[1])

	store.Close(encoder)

	datastore.RemoveStore(store.GetName())

	poolLength := utils.MaxPoolLength

	defer func() {

		utils.MaxPoolLength = poolLength
	}()

	utils.MaxPoolLength = 100

	batch := packStatusDurationBatch(plugin, time.Now().Unix(), stringValues)

	writer.bufferBytes = batch[utils.Buffer].([]byte)

	writer.writeStatusMetric(batch)

	utils.AssertLogMessage(assertions, "Vertical Writer", "writer", "failed to acquire store")

}

func TestHorizontalWriterV3(t *testing.T) {

	assertions := assert.New(t)

	maxStringBytes := utils.MaxStringBytes

	txnBufferBytes := utils.GetDataWriterTxnBufferBytes()

	valueBufferBytes := utils.GetDataWriterValueBufferBytes()

	defer func() {

		utils.MaxStringBytes = maxStringBytes

		utils.AlterConfigs("datastore.data.writer.txn.buffer.bytes", txnBufferBytes)

		utils.AlterConfigs("datastore.data.writer.value.buffer.bytes", valueBufferBytes)

	}()

	utils.MaxStringBytes = 0

	utils.AlterConfigs("datastore.data.writer.txn.buffer.bytes", 0)

	utils.AlterConfigs("datastore.data.writer.value.buffer.bytes", 0)

	writer := NewHorizontalWriter(0)

	writer.Start()

	writer.ShutdownNotifications <- true

	time.Sleep(time.Second)

	utils.AssertLogMessage(assertions, "Horizontal Writer", "writer", "occurred while mapping annonymous buffer for horizontal writer")

}

func TestIndexWriterV3(t *testing.T) {

	assertions := assert.New(t)

	maxBufferBytes := utils.MaxValueBufferBytes

	defer func() {

		utils.MaxValueBufferBytes = maxBufferBytes

	}()

	utils.MaxValueBufferBytes = 0

	NewIndexWriter(0)

	utils.AssertLogMessage(assertions, "Index Writer", "writer", "occurred while mapping annonymous buffer for index writer")
}

func TestStaticMetricWriterV1(t *testing.T) {

	assertions := assert.New(t)

	maxBufferBytes := utils.MaxValueBufferBytes

	defer func() {

		utils.MaxValueBufferBytes = maxBufferBytes

	}()

	utils.MaxValueBufferBytes = 0

	writer := NewStaticMetricWriter(0)

	writer.Start()

	writer.tokenizer = nil

	writer.encoder.MemoryPool = nil

	writer.Events <- utils.MotadataMap{
		utils.Plugin: "dummy",
	}

	time.Sleep(time.Second)

	writer.ShutdownNotifications <- true

	time.Sleep(time.Second)

	utils.MaxValueBufferBytes = maxBufferBytes

	writer = NewStaticMetricWriter(0)

	writer.Start()

	pluginName := "dummy.static"

	store := datastore.GetStore(pluginName, utils.StaticMetric, true, true, encoder, tokenizer)

	key := []byte("dummy.key^0")

	store.Put(key, []byte("12345678dummy.value"), encoder, tokenizer)

	store.Delete(key, encoder, tokenizer)

	writer.Events <- utils.MotadataMap{
		utils.Plugin: pluginName,
		"key":        "dummy.key",
	}

	time.Sleep(time.Second)

	utils.AssertLogMessage(assertions, "Static Metric Writer", "writer", "occurred while mapping annonymous buffer for static metric writer")
}

func TestReadStringColumn(t *testing.T) {

	assertions := assert.New(t)

	verticalWriter := NewVerticalWriter(50, []*StaticMetricWriter{staticMetricWriter})

	verticalWriter.Start()

	buffer := bytes2.Buffer{}

	empty := ""

	data := make([]byte, 4)

	binary.LittleEndian.PutUint32(data, uint32(len(empty)))

	buffer.Write(data)

	buffer.WriteString(empty)

	verticalWriter.bufferBytes = buffer.Bytes()

	value, err := verticalWriter.readStringColumn()

	assertions.Equal(value, "")

	assertions.NoError(err)

}

func TestNetRouteEvent(t *testing.T) {

	assertions := assert.New(t)

	verticalWriter := NewVerticalWriter(50, []*StaticMetricWriter{staticMetricWriter})

	verticalWriter.Start()

	buffer := bytes2.Buffer{}

	empty := ""

	data := make([]byte, 4)

	binary.LittleEndian.PutUint32(data, uint32(len(empty)))

	buffer.Write(data[:2])

	buffer.WriteString(empty)

	verticalWriter.bufferBytes = buffer.Bytes()

	verticalWriter.processNetRouteEvent("", "")

	utils.AssertLogMessage(assertions, "Vertical Writer", "writer", "failed to read string column, reason")

	assertions.Error(verticalWriter.flushMetricCacheEntries("", nil))

	datastore.GetStore("dummy-netroute", utils.NetRouteMetric, true, true, encoder, tokenizer)

	temp := map[string][][]interface{}{}

	temp["dummy"] = nil

	assertions.NoError(verticalWriter.flushMetricCacheEntries("^dummy-netroute", temp))

}

func TestStatusMetricBatch(t *testing.T) {

	assertions := assert.New(t)

	verticalWriter := NewVerticalWriter(50, []*StaticMetricWriter{staticMetricWriter})

	verticalWriter.Start()

	buffer := bytes2.Buffer{}

	empty := ""

	data := make([]byte, 4)

	binary.LittleEndian.PutUint32(data, uint32(len(empty)))

	buffer.Write(data[:2])

	buffer.WriteString(empty)

	verticalWriter.bufferBytes = buffer.Bytes()

	verticalWriter.processStatusMetricBatch()

	verticalWriter.position = 0

	buffer.Reset()

	buffer.Write(data)

	buffer.WriteString(empty)

	buffer.Write(data[:2])

	buffer.WriteString(empty)

	verticalWriter.bufferBytes = buffer.Bytes()

	verticalWriter.processStatusMetricBatch()

	verticalWriter.position = 0

	buffer.Reset()

	for i := 0; i < 2; i += 1 {

		buffer.Write(data)

		buffer.WriteString(empty)

	}

	buffer.Write(data[:2])

	buffer.WriteString(empty)

	verticalWriter.bufferBytes = buffer.Bytes()

	verticalWriter.processStatusMetricBatch()

	verticalWriter.position = 0

	buffer.Reset()

	for i := 0; i < 3; i += 1 {

		buffer.Write(data)

		buffer.WriteString(empty)

	}

	buffer.Write(data[:2])

	buffer.WriteString(empty)

	verticalWriter.bufferBytes = buffer.Bytes()

	verticalWriter.processStatusMetricBatch()

	verticalWriter.position = 0

	buffer.Reset()

	for i := 0; i < 4; i += 1 {

		buffer.Write(data)

		buffer.WriteString(empty)

	}

	buffer.Write(data[:2])

	buffer.WriteString(empty)

	verticalWriter.bufferBytes = buffer.Bytes()

	verticalWriter.processStatusMetricBatch()

	verticalWriter.position = 0

	utils.AssertLogMessage(assertions, "Vertical Writer", "writer", "occurred while reading status column, for plugin")

}

func TestProcessStatusFlapBatch(t *testing.T) {

	assertions := assert.New(t)

	verticalWriter := NewVerticalWriter(50, []*StaticMetricWriter{staticMetricWriter})

	verticalWriter.Start()

	verticalWriter.bufferBytes = nil

	verticalWriter.processStatusFlapBatch(10, 0)

	utils.AssertLogMessage(assertions, "Vertical Writer", "writer", " occurred while decode store type")

	buffer := bytes2.Buffer{}

	empty := ""

	data := make([]byte, 4)

	binary.LittleEndian.PutUint32(data, uint32(len(empty)))

	buffer.Write(data)

	buffer.WriteString(empty)

	buffer.Write(data[:2])

	verticalWriter.bufferBytes = buffer.Bytes()

	verticalWriter.processStatusFlapBatch(10, 0)

	verticalWriter.position = 0

	buffer.Reset()

	buffer.Write(data)

	buffer.WriteString(empty)

	buffer.Write(data)

	buffer.WriteString(empty)

	buffer.WriteByte(0)

	buffer.Write(data[:2])

	verticalWriter.bufferBytes = buffer.Bytes()

	verticalWriter.processStatusFlapBatch(15, 0)

	verticalWriter.position = 0

	buffer.Reset()

	buffer.Write(data)

	buffer.WriteString(empty)

	buffer.Write(data)

	buffer.WriteString(empty)

	buffer.WriteByte(0)

	metric := "metric"

	binary.LittleEndian.PutUint32(data, uint32(len(metric)))

	buffer.Write(data)

	buffer.WriteString(metric)

	buffer.Write(data[:2])

	verticalWriter.bufferBytes = buffer.Bytes()

	verticalWriter.processStatusFlapBatch(15, 0)

	utils.AssertLogMessage(assertions, "Vertical Writer", "writer", "occurred while reading value column for objectId")

}

func TestPerformanceMetricBatch(t *testing.T) {

	assertions := assert.New(t)

	verticalWriter := NewVerticalWriter(50, []*StaticMetricWriter{staticMetricWriter})

	verticalWriter.Start()

	buffer := bytes2.Buffer{}

	empty := ""

	data := make([]byte, 4)

	binary.LittleEndian.PutUint32(data, uint32(len(empty)))

	buffer.Write(data)

	buffer.WriteString(empty)

	buffer.Write(data[:2])

	verticalWriter.bufferBytes = buffer.Bytes()

	verticalWriter.processPerformanceBatch(10, 0)

	utils.AssertLogMessage(assertions, "Vertical Writer", "writer", "occurred while reading instance column for objectId")

}

//helper function

func packStatusDurationBatch(pluginId string, tick int64, values []string) (statusDurationBatch utils.MotadataMap) {

	//report datastore packing logic

	buffer := &bytes2.Buffer{}

	EncodeINT32Value(0, buffer)

	//objectId --> instance --> status -->reason-->column -->duration

	objectId := values[0]

	instance := values[1]

	status := values[2]

	reason := values[3]

	column := values[4]

	duration := StringToINT32(values[5])

	EncodeINT32Value(int32(len(objectId)), buffer)

	buffer.WriteString(objectId)

	EncodeINT32Value(int32(len(instance)), buffer)

	buffer.WriteString(instance)

	EncodeINT32Value(int32(len(status)), buffer)

	buffer.WriteString(status)

	EncodeINT32Value(int32(len(reason)), buffer)

	buffer.WriteString(reason)

	EncodeINT32Value(int32(len(column)), buffer)

	buffer.WriteString(column)

	EncodeINT32Value(duration, buffer)

	EncodeINT32ValueAt(int32(buffer.Len()-4), 0, buffer)

	buffer.Write(utils.EOTBytes)

	statusDurationBatch = utils.MotadataMap{
		utils.Plugin:            pluginId,
		utils.Tick:              tick,
		utils.Buffer:            buffer.Bytes(),
		utils.BatchSize:         1,
		datastore.DatastoreType: utils.StatusFlapHistory,
	}

	return
}

func packMetricBatchV4(columns []string, values []interface{}, dataTypes []byte, objectId int32, instance string) []byte {

	buffer := &bytes2.Buffer{}

	EncodeINT32Value(objectId, buffer)

	EncodeINT32Value(int32(len(instance)), buffer)

	buffer.WriteString(instance)

	for index, column := range columns {

		buffer.WriteByte(dataTypes[index])

		EncodeINT32Value(int32(len(column)), buffer)

		buffer.WriteString(column)

		if dataTypes[index] == datastore.StringColumn {

			value := ToString(values[index])

			EncodeINT32Value(int32(len(value)), buffer)

			buffer.WriteString(value)

		} else if dataTypes[index] == datastore.IntegerColumn {

			EncodeINT64Value(int64(ToINT(values[index])), buffer)

		} else {

			EncodeFLOAT64Value(utils.ToFlOAT(values[index]), buffer)

		}

	}

	return buffer.Bytes()
}

func (writer *HorizontalWriter) clearContext() {

	clear(writer.int32Fields)

	clear(writer.int64Fields)

	clear(writer.stringFields)

	writer.valueElementSize = utils.NotAvailable

}
