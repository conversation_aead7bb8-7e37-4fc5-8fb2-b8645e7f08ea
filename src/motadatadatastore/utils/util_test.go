/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/* Change Logs:
* Date                   Author          		Notes
* 2025-04-02			 Vedant Dokania		    Motadata-4859  Added Test Case For Qualifying Week Days
* 2025-04-21             Vedant Dokania         Motadata-4859  Refactored method name StringToBytesReadonly
* 2025-05-05			 Swapnil A. Dave		MOTADATA-6078 passed defaultblobpool const as the function parameter in memory pool initialization
* 2025-05-07             <PERSON><PERSON><PERSON>            Motadata-6073  Added Test Cases for RedirectStderr
* 2025-05-15			 Vedant Dokania			Motadata-6251  Unpacking changes for the testcases
 */

package utils

import (
	"bytes"
	"context"
	"encoding/binary"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/ClickHouse/clickhouse-go/v2"
	"github.com/go-faster/city"
	"github.com/stretchr/testify/assert"
	"math"
	"math/rand"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
	"testing"
	"time"
)

func TestMain(m *testing.M) {

	if SkipBenchmarkTest() {

		return
	}

	EnvironmentType = DatastoreTestEnvironment

	bytes, err := os.ReadFile(filepath.Dir(filepath.Dir(filepath.Dir(CurrentDir))) + PathSeparator + ConfigDir + PathSeparator + MotadataDatastoreConfigFile)

	if err != nil {
		panic(err)
	}

	motadataConfigBytes, _ := json.Marshal(MotadataMap{
		"manager.id":        "dummy_testing_id",
		"installation.mode": "PRIMARY",
	})

	os.MkdirAll(CurrentDir+PathSeparator+ConfigDir, 0755)

	os.WriteFile(CurrentDir+PathSeparator+ConfigDir+PathSeparator+MotadataDataConfigFile, motadataConfigBytes, 0755)

	os.RemoveAll(CurrentDir + PathSeparator + LogDirectory)

	if InitConfigs(bytes) {

		runtime.GC()

		m.Run()
	}

}

func TestSetConfigs(t *testing.T) {

	bytes, err := os.ReadFile(filepath.Dir(filepath.Dir(filepath.Dir(CurrentDir))) + PathSeparator + ConfigDir + PathSeparator + MotadataDatastoreConfigFile)

	assert.Nil(t, err)

	EnvironmentType = DatastoreTestEnvironment

	InitConfigs(bytes)

	assert.Equal(t, configs.IsNotEmpty(), true)

}

func TestRoundOffSeconds(t *testing.T) {

	tick := UnixToSeconds(time.Date(2024, 01, 25, 23, 59, 59, 59, time.UTC).Unix())

	assertions := assert.New(t)

	assertions.Equal(time.Unix(SecondsToUnix(RoundOffSeconds(tick, 5)), 0).UTC(), time.Date(2024, 01, 25, 23, 55, 0, 0, time.UTC))

	assertions.Equal(time.Unix(SecondsToUnix(RoundOffSeconds(tick, 10)), 0).UTC(), time.Date(2024, 01, 25, 23, 50, 0, 0, time.UTC))

	assertions.Equal(time.Unix(SecondsToUnix(RoundOffSeconds(tick, 15)), 0).UTC(), time.Date(2024, 01, 25, 23, 45, 0, 0, time.UTC))

	assertions.Equal(time.Unix(SecondsToUnix(RoundOffSeconds(tick, 20)), 0).UTC(), time.Date(2024, 01, 25, 23, 40, 0, 0, time.UTC))

	assertions.Equal(time.Unix(SecondsToUnix(RoundOffSeconds(tick, 30)), 0).UTC(), time.Date(2024, 01, 25, 23, 30, 0, 0, time.UTC))

	assertions.Equal(time.Unix(SecondsToUnix(RoundOffSeconds(tick, 60)), 0).UTC(), time.Date(2024, 01, 25, 23, 0, 0, 0, time.UTC))

	assertions.Equal(time.Unix(SecondsToUnix(RoundOffSeconds(tick, 120)), 0).UTC(), time.Date(2024, 01, 25, 22, 0, 0, 0, time.UTC))

	assertions.Equal(time.Unix(SecondsToUnix(RoundOffSeconds(tick, 180)), 0).UTC(), time.Date(2024, 01, 25, 21, 0, 0, 0, time.UTC))

	assertions.Equal(time.Unix(SecondsToUnix(RoundOffSeconds(tick, 240)), 0).UTC(), time.Date(2024, 01, 25, 20, 0, 0, 0, time.UTC))

	assertions.Equal(time.Unix(SecondsToUnix(RoundOffSeconds(tick, 300)), 0).UTC(), time.Date(2024, 01, 25, 20, 0, 0, 0, time.UTC))

	assertions.Equal(time.Unix(SecondsToUnix(RoundOffSeconds(tick, 360)), 0).UTC(), time.Date(2024, 01, 25, 18, 0, 0, 0, time.UTC))

	assertions.Equal(time.Unix(SecondsToUnix(RoundOffSeconds(tick, 420)), 0).UTC(), time.Date(2024, 01, 25, 21, 0, 0, 0, time.UTC))

	assertions.Equal(time.Unix(SecondsToUnix(RoundOffSeconds(tick, 480)), 0).UTC(), time.Date(2024, 01, 25, 16, 0, 0, 0, time.UTC))

	assertions.Equal(time.Unix(SecondsToUnix(RoundOffSeconds(tick, 540)), 0).UTC(), time.Date(2024, 01, 25, 18, 0, 0, 0, time.UTC))

	assertions.Equal(time.Unix(SecondsToUnix(RoundOffSeconds(tick, 600)), 0).UTC(), time.Date(2024, 01, 25, 20, 0, 0, 0, time.UTC))

	assertions.Equal(time.Unix(SecondsToUnix(RoundOffSeconds(tick, 660)), 0).UTC(), time.Date(2024, 01, 25, 22, 0, 0, 0, time.UTC))

	assertions.Equal(time.Unix(SecondsToUnix(RoundOffSeconds(tick, 720)), 0).UTC(), time.Date(2024, 01, 25, 12, 0, 0, 0, time.UTC))

	assertions.Equal(time.Unix(SecondsToUnix(RoundOffSeconds(tick, 1440)), 0).UTC(), time.Date(2024, 01, 25, 00, 0, 0, 0, time.UTC))

	tick = UnixToSeconds(time.Date(2024, 01, 25, 0, 0, 0, 0, time.UTC).Unix())

	assertions.Equal(time.Unix(SecondsToUnix(RoundOffSeconds(tick, 5)), 0).UTC(), time.Date(2024, 01, 25, 00, 0, 0, 0, time.UTC))

	assertions.Equal(time.Unix(SecondsToUnix(RoundOffSeconds(tick, 10)), 0).UTC(), time.Date(2024, 01, 25, 00, 0, 0, 0, time.UTC))

	assertions.Equal(time.Unix(SecondsToUnix(RoundOffSeconds(tick, 15)), 0).UTC(), time.Date(2024, 01, 25, 00, 0, 0, 0, time.UTC))

	assertions.Equal(time.Unix(SecondsToUnix(RoundOffSeconds(tick, 30)), 0).UTC(), time.Date(2024, 01, 25, 00, 0, 0, 0, time.UTC))

	assertions.Equal(time.Unix(SecondsToUnix(RoundOffSeconds(tick, 60)), 0).UTC(), time.Date(2024, 01, 25, 00, 0, 0, 0, time.UTC))

	assertions.Equal(time.Unix(SecondsToUnix(RoundOffSeconds(tick, 120)), 0).UTC(), time.Date(2024, 01, 25, 00, 0, 0, 0, time.UTC))

	assertions.Equal(time.Unix(SecondsToUnix(RoundOffSeconds(tick, 180)), 0).UTC(), time.Date(2024, 01, 25, 00, 0, 0, 0, time.UTC))

	assertions.Equal(time.Unix(SecondsToUnix(RoundOffSeconds(tick, 240)), 0).UTC(), time.Date(2024, 01, 25, 00, 0, 0, 0, time.UTC))

	assertions.Equal(time.Unix(SecondsToUnix(RoundOffSeconds(tick, 300)), 0).UTC(), time.Date(2024, 01, 25, 00, 0, 0, 0, time.UTC))

	assertions.Equal(time.Unix(SecondsToUnix(RoundOffSeconds(tick, 360)), 0).UTC(), time.Date(2024, 01, 25, 00, 0, 0, 0, time.UTC))

	assertions.Equal(time.Unix(SecondsToUnix(RoundOffSeconds(tick, 720)), 0).UTC(), time.Date(2024, 01, 25, 00, 0, 0, 0, time.UTC))

	assertions.Equal(time.Unix(SecondsToUnix(RoundOffSeconds(tick, 1440)), 0).UTC(), time.Date(2024, 01, 25, 00, 0, 0, 0, time.UTC))

}

func TestRoundOffUnixSeconds(t *testing.T) {

	tick := time.Date(2024, 01, 25, 23, 59, 59, 59, time.UTC).Unix()

	assertions := assert.New(t)

	assertions.Equal(time.Unix(RoundOffUnixSeconds(tick, 5), 0).UTC(), time.Date(2024, 01, 25, 23, 55, 0, 0, time.UTC))

	assertions.Equal(time.Unix(RoundOffUnixSeconds(tick, 10), 0).UTC(), time.Date(2024, 01, 25, 23, 50, 0, 0, time.UTC))

	assertions.Equal(time.Unix(RoundOffUnixSeconds(tick, 15), 0).UTC(), time.Date(2024, 01, 25, 23, 45, 0, 0, time.UTC))

	assertions.Equal(time.Unix(RoundOffUnixSeconds(tick, 20), 0).UTC(), time.Date(2024, 01, 25, 23, 40, 0, 0, time.UTC))

	assertions.Equal(time.Unix(RoundOffUnixSeconds(tick, 30), 0).UTC(), time.Date(2024, 01, 25, 23, 30, 0, 0, time.UTC))

	assertions.Equal(time.Unix(RoundOffUnixSeconds(tick, 60), 0).UTC(), time.Date(2024, 01, 25, 23, 0, 0, 0, time.UTC))

	assertions.Equal(time.Unix(RoundOffUnixSeconds(tick, 120), 0).UTC(), time.Date(2024, 01, 25, 22, 0, 0, 0, time.UTC))

	assertions.Equal(time.Unix(RoundOffUnixSeconds(tick, 180), 0).UTC(), time.Date(2024, 01, 25, 21, 0, 0, 0, time.UTC))

	assertions.Equal(time.Unix(RoundOffUnixSeconds(tick, 240), 0).UTC(), time.Date(2024, 01, 25, 20, 0, 0, 0, time.UTC))

	assertions.Equal(time.Unix(RoundOffUnixSeconds(tick, 300), 0).UTC(), time.Date(2024, 01, 25, 20, 0, 0, 0, time.UTC))

	assertions.Equal(time.Unix(RoundOffUnixSeconds(tick, 360), 0).UTC(), time.Date(2024, 01, 25, 18, 0, 0, 0, time.UTC))

	assertions.Equal(time.Unix(RoundOffUnixSeconds(tick, 420), 0).UTC(), time.Date(2024, 01, 25, 21, 0, 0, 0, time.UTC))

	assertions.Equal(time.Unix(RoundOffUnixSeconds(tick, 480), 0).UTC(), time.Date(2024, 01, 25, 16, 0, 0, 0, time.UTC))

	assertions.Equal(time.Unix(RoundOffUnixSeconds(tick, 540), 0).UTC(), time.Date(2024, 01, 25, 18, 0, 0, 0, time.UTC))

	assertions.Equal(time.Unix(RoundOffUnixSeconds(tick, 600), 0).UTC(), time.Date(2024, 01, 25, 20, 0, 0, 0, time.UTC))

	assertions.Equal(time.Unix(RoundOffUnixSeconds(tick, 660), 0).UTC(), time.Date(2024, 01, 25, 22, 0, 0, 0, time.UTC))

	assertions.Equal(time.Unix(RoundOffUnixSeconds(tick, 720), 0).UTC(), time.Date(2024, 01, 25, 12, 0, 0, 0, time.UTC))

	assertions.Equal(time.Unix(RoundOffUnixSeconds(tick, 1440), 0).UTC(), time.Date(2024, 01, 25, 00, 0, 0, 0, time.UTC))

	tick = time.Date(2024, 01, 25, 0, 0, 0, 0, time.UTC).Unix()

	assertions.Equal(time.Unix(RoundOffUnixSeconds(tick, 5), 0).UTC(), time.Date(2024, 01, 25, 00, 0, 0, 0, time.UTC))

	assertions.Equal(time.Unix(RoundOffUnixSeconds(tick, 10), 0).UTC(), time.Date(2024, 01, 25, 00, 0, 0, 0, time.UTC))

	assertions.Equal(time.Unix(RoundOffUnixSeconds(tick, 15), 0).UTC(), time.Date(2024, 01, 25, 00, 0, 0, 0, time.UTC))

	assertions.Equal(time.Unix(RoundOffUnixSeconds(tick, 30), 0).UTC(), time.Date(2024, 01, 25, 00, 0, 0, 0, time.UTC))

	assertions.Equal(time.Unix(RoundOffUnixSeconds(tick, 60), 0).UTC(), time.Date(2024, 01, 25, 00, 0, 0, 0, time.UTC))

	assertions.Equal(time.Unix(RoundOffUnixSeconds(tick, 120), 0).UTC(), time.Date(2024, 01, 25, 00, 0, 0, 0, time.UTC))

	assertions.Equal(time.Unix(RoundOffUnixSeconds(tick, 180), 0).UTC(), time.Date(2024, 01, 25, 00, 0, 0, 0, time.UTC))

	assertions.Equal(time.Unix(RoundOffUnixSeconds(tick, 240), 0).UTC(), time.Date(2024, 01, 25, 00, 0, 0, 0, time.UTC))

	assertions.Equal(time.Unix(RoundOffUnixSeconds(tick, 300), 0).UTC(), time.Date(2024, 01, 25, 00, 0, 0, 0, time.UTC))

	assertions.Equal(time.Unix(RoundOffUnixSeconds(tick, 360), 0).UTC(), time.Date(2024, 01, 25, 00, 0, 0, 0, time.UTC))

	assertions.Equal(time.Unix(RoundOffUnixSeconds(tick, 720), 0).UTC(), time.Date(2024, 01, 25, 00, 0, 0, 0, time.UTC))

	assertions.Equal(time.Unix(RoundOffUnixSeconds(tick, 1440), 0).UTC(), time.Date(2024, 01, 25, 00, 0, 0, 0, time.UTC))

}

func TestRoundOffUnixMilliSeconds(t *testing.T) {

	tick := time.Date(2024, 01, 25, 23, 59, 59, 59, time.UTC).UnixMilli()

	assertions := assert.New(t)

	assertions.Equal(time.UnixMilli(RoundOffUnixMilliSeconds(tick, 5)).UTC(), time.Date(2024, 01, 25, 23, 55, 0, 0, time.UTC))

	assertions.Equal(time.UnixMilli(RoundOffUnixMilliSeconds(tick, 10)).UTC(), time.Date(2024, 01, 25, 23, 50, 0, 0, time.UTC))

	assertions.Equal(time.UnixMilli(RoundOffUnixMilliSeconds(tick, 15)).UTC(), time.Date(2024, 01, 25, 23, 45, 0, 0, time.UTC))

	assertions.Equal(time.UnixMilli(RoundOffUnixMilliSeconds(tick, 20)).UTC(), time.Date(2024, 01, 25, 23, 40, 0, 0, time.UTC))

	assertions.Equal(time.UnixMilli(RoundOffUnixMilliSeconds(tick, 30)).UTC(), time.Date(2024, 01, 25, 23, 30, 0, 0, time.UTC))

	assertions.Equal(time.UnixMilli(RoundOffUnixMilliSeconds(tick, 60)).UTC(), time.Date(2024, 01, 25, 23, 0, 0, 0, time.UTC))

	assertions.Equal(time.UnixMilli(RoundOffUnixMilliSeconds(tick, 120)).UTC(), time.Date(2024, 01, 25, 22, 0, 0, 0, time.UTC))

	assertions.Equal(time.UnixMilli(RoundOffUnixMilliSeconds(tick, 180)).UTC(), time.Date(2024, 01, 25, 21, 0, 0, 0, time.UTC))

	assertions.Equal(time.UnixMilli(RoundOffUnixMilliSeconds(tick, 240)).UTC(), time.Date(2024, 01, 25, 20, 0, 0, 0, time.UTC))

	assertions.Equal(time.UnixMilli(RoundOffUnixMilliSeconds(tick, 300)).UTC(), time.Date(2024, 01, 25, 20, 0, 0, 0, time.UTC))

	assertions.Equal(time.UnixMilli(RoundOffUnixMilliSeconds(tick, 360)).UTC(), time.Date(2024, 01, 25, 18, 0, 0, 0, time.UTC))

	assertions.Equal(time.UnixMilli(RoundOffUnixMilliSeconds(tick, 420)).UTC(), time.Date(2024, 01, 25, 21, 0, 0, 0, time.UTC))

	assertions.Equal(time.UnixMilli(RoundOffUnixMilliSeconds(tick, 480)).UTC(), time.Date(2024, 01, 25, 16, 0, 0, 0, time.UTC))

	assertions.Equal(time.UnixMilli(RoundOffUnixMilliSeconds(tick, 540)).UTC(), time.Date(2024, 01, 25, 18, 0, 0, 0, time.UTC))

	assertions.Equal(time.UnixMilli(RoundOffUnixMilliSeconds(tick, 600)).UTC(), time.Date(2024, 01, 25, 20, 0, 0, 0, time.UTC))

	assertions.Equal(time.UnixMilli(RoundOffUnixMilliSeconds(tick, 660)).UTC(), time.Date(2024, 01, 25, 22, 0, 0, 0, time.UTC))

	assertions.Equal(time.UnixMilli(RoundOffUnixMilliSeconds(tick, 720)).UTC(), time.Date(2024, 01, 25, 12, 0, 0, 0, time.UTC))

	assertions.Equal(time.UnixMilli(RoundOffUnixMilliSeconds(tick, 1440)).UTC(), time.Date(2024, 01, 25, 00, 0, 0, 0, time.UTC))

	tick = time.Date(2024, 01, 25, 0, 0, 0, 0, time.UTC).UnixMilli()

	assertions.Equal(time.UnixMilli(RoundOffUnixMilliSeconds(tick, 5)).UTC(), time.Date(2024, 01, 25, 00, 0, 0, 0, time.UTC))

	assertions.Equal(time.UnixMilli(RoundOffUnixMilliSeconds(tick, 10)).UTC(), time.Date(2024, 01, 25, 00, 0, 0, 0, time.UTC))

	assertions.Equal(time.UnixMilli(RoundOffUnixMilliSeconds(tick, 15)).UTC(), time.Date(2024, 01, 25, 00, 0, 0, 0, time.UTC))

	assertions.Equal(time.UnixMilli(RoundOffUnixMilliSeconds(tick, 30)).UTC(), time.Date(2024, 01, 25, 00, 0, 0, 0, time.UTC))

	assertions.Equal(time.UnixMilli(RoundOffUnixMilliSeconds(tick, 60)).UTC(), time.Date(2024, 01, 25, 00, 0, 0, 0, time.UTC))

	assertions.Equal(time.UnixMilli(RoundOffUnixMilliSeconds(tick, 120)).UTC(), time.Date(2024, 01, 25, 00, 0, 0, 0, time.UTC))

	assertions.Equal(time.UnixMilli(RoundOffUnixMilliSeconds(tick, 180)).UTC(), time.Date(2024, 01, 25, 00, 0, 0, 0, time.UTC))

	assertions.Equal(time.UnixMilli(RoundOffUnixMilliSeconds(tick, 240)).UTC(), time.Date(2024, 01, 25, 00, 0, 0, 0, time.UTC))

	assertions.Equal(time.UnixMilli(RoundOffUnixMilliSeconds(tick, 300)).UTC(), time.Date(2024, 01, 25, 00, 0, 0, 0, time.UTC))

	assertions.Equal(time.UnixMilli(RoundOffUnixMilliSeconds(tick, 360)).UTC(), time.Date(2024, 01, 25, 00, 0, 0, 0, time.UTC))

	assertions.Equal(time.UnixMilli(RoundOffUnixMilliSeconds(tick, 720)).UTC(), time.Date(2024, 01, 25, 00, 0, 0, 0, time.UTC))

	assertions.Equal(time.UnixMilli(RoundOffUnixMilliSeconds(tick, 1440)).UTC(), time.Date(2024, 01, 25, 00, 0, 0, 0, time.UTC))

}

func TestMotadataMap(t *testing.T) {

	testMap := make(MotadataMap)

	assert.Equal(t, testMap.IsNotEmpty(), false)

	testMap["key1"] = "value1"

	assert.Equal(t, testMap.GetMapKeys(), []string{"key1"})

	assert.Equal(t, testMap.GetMapValues(), []interface{}{"value1"})

	assert.Equal(t, testMap.IsNotEmpty(), true)

	assert.Equal(t, testMap.Contains("key1"), true)

	assert.Equal(t, testMap.Contains("key2"), false)

	assert.Equal(t, testMap.GetStringValue("key1"), "value1")

	testMap["key1"] = 5

	assert.Equal(t, testMap.GetIntValue("key1"), 5)

	assert.Equal(t, testMap.GetInt64Value("key1"), int64(5))

	assert.Equal(t, testMap.GetStringValue("key1"), "5")

	testMap["key2"] = make(MotadataMap)

	assert.NotEqual(t, testMap.GetMapValue("key2"), nil)

	testMap.Delete("key2")

	assert.Equal(t, testMap.Contains("key2"), false)

	testMap["key3"] = "9"

	assert.Equal(t, testMap.GetIntValue("key3"), 9)

	assert.Equal(t, testMap.GetInt64Value("key3"), int64(9))

	testMap["key3"] = 9.0

	assert.Equal(t, testMap.GetIntValue("key3"), 9)

	assert.Equal(t, testMap.GetInt64Value("key3"), int64(9))

	assert.Equal(t, testMap.GetStringValue("key3"), "9")

	testMap["key3"] = int64(9)

	assert.Equal(t, testMap.GetIntValue("key3"), 9)

	assert.Equal(t, testMap.GetInt64Value("key3"), int64(9))

	assert.Equal(t, testMap.GetStringValue("key3"), "9")

	assert.Equal(t, testMap.GetSliceValue("key4"), []interface{}(nil))

	testMap.Clear()

	assert.Equal(t, testMap.IsNotEmpty(), false)

	testMap["key4"] = []string{"abc", "def"}

	assert.Equal(t, testMap.GetSliceStringValue("key4"), []string{"abc", "def"})

	testMap["key5"] = int32(2)

	assert.Equal(t, testMap.GetInt32Value("key5"), int32(2))

	assert.Equal(t, testMap.GetIntValue("key5"), 2)

	assert.Equal(t, testMap.GetStringValue("key5"), "2")

	testMap["key5"] = "2 "

	assert.Equal(t, testMap.GetInt32Value("key5"), int32(2))

	testMap["key5"] = float64(2)

	assert.Equal(t, testMap.GetInt32Value("key5"), int32(2))

	testMap["key5"] = float32(2)

	assert.Equal(t, testMap.GetInt32Value("key5"), int32(2))

	testMap["key6"] = float64(2)

	assert.Equal(t, testMap.GetFloatValue("key6"), float64(2))

	assert.Equal(t, testMap.GetValue("key6"), float64(2))

	testMap["key6"] = 3

	assert.Equal(t, testMap.GetFloatValue("key6"), float64(3))

	testMap["key6"] = int8(3)

	assert.Equal(t, testMap.GetIntValue("key6"), 3)

	testMap["key6"] = int16(3)

	assert.Equal(t, testMap.GetIntValue("key6"), 3)

	testMap["key6"] = int32(3)

	assert.Equal(t, testMap.GetIntValue("key6"), 3)

	testMap["key7"] = uint64(2)

	assert.Equal(t, testMap.GetUInt64Value("key7"), uint64(2))

	testMap["key7"] = "2 "

	assert.Equal(t, testMap.GetUInt64Value("key7"), uint64(2))

	testMap["key7"] = 2

	assert.Equal(t, testMap.GetUInt64Value("key7"), uint64(2))

	testMap["key7"] = int64(2)

	assert.Equal(t, testMap.GetUInt64Value("key7"), uint64(2))

	testMap["key7"] = float64(2)

	assert.Equal(t, testMap.GetUInt64Value("key7"), uint64(2))

	testMap["key7"] = uint8(1)

	assert.Equal(t, testMap.GetIntValue("key7"), 1)

	testMap["key8"] = "hello"

	assert.Equal(t, testMap.GetStringValue("key8"), "hello")

	testMap["key9"] = []int32{1}

	assert.Equal(t, testMap.GetSliceInt32Value("key9"), []int32{1})

	testMap["key10"] = []interface{}{1, 2}

	assert.Equal(t, testMap.GetListValue("key10"), []interface{}{1, 2})

	concurrentTestMap := testMap.ToConcurrentMap()

	value, _ := concurrentTestMap.Get("key10")

	assert.Equal(t, value, []interface{}{1, 2})

	assert.Equal(t, ToMap(testMap).GetListValue("key10"), []interface{}{1, 2})

	testMap1 := make(map[string]interface{})

	testMap1["key1"] = 1

	assert.Equal(t, ToMap(testMap1).GetIntValue("key1"), 1)

	testMap["key11"] = []interface{}{testMap1}

	assert.Equal(t, testMap.GetMapListValue("key11"), []MotadataMap{testMap1})
}

func TestStringFunctions(t *testing.T) {

	assert.Equal(t, GetStringValue("hello"), "hello")

	assert.Equal(t, GetStringValue(int8(1)), "1")

	assert.Equal(t, GetStringValue(1), "1")

	assert.Equal(t, GetStringValue(int64(1)), "1")

	assert.Equal(t, GetStringValue(int32(1)), "1")

	assert.Equal(t, GetStringValue(float32(1)), "1.00")

	assert.Equal(t, GetStringValue(float64(1)), "1.00")

	assert.Equal(t, GetStringValue(true), "true")

	assert.Equal(t, ToStringList([]interface{}{"hello", "hello1"}), []string{"hello", "hello1"})

	assert.Equal(t, GetStringSliceSize([]string{"hello"}), 5)

	value := GetStringValue([]uint8{1})

	assert.Equal(t, len(value), 1)

	value = GetStringValue([]byte("123"))

	assert.Equal(t, value, "123")

	value = GetStringValue(math.MaxFloat64)

	assert.Equal(t, value, "179769313486231570814527423731704356798070567525844996598917476803157260780028538760589558632766878171540458953514382464234321326889464182768467546703537516986049910576551282076245490090389328944075868508455133942304583236903222948165808559332123348274797826204144723168738177180919299881250404026184124858368.00")

	bytes := []byte{1}

	assert.Equal(t, Reverse(bytes), bytes)

	assert.Equal(t, PrependByte(bytes, 1), []byte{1, 1})

	assert.Equal(t, PrependBytes(bytes, bytes), []byte{1, 1})

	assert.Equal(t, removeListDuplicateValues([]interface{}{1, 1}), []interface{}{1})

}

func TestBitmapFunctions(t *testing.T) {

	var stringBitmaps = make(map[string][]byte)

	stringBitmaps["key1"] = []byte{1}

	assert.Equal(t, GetStringBitmapMapSize(stringBitmaps), 5)

	var int8Bitmaps = make(map[int8][]byte)

	int8Bitmaps[int8(1)] = []byte{1}

	assert.Equal(t, GetINT8BitmapMapSize(int8Bitmaps), 2)

	var int16Bitmaps = make(map[int16][]byte)

	int16Bitmaps[int16(1)] = []byte{1}

	assert.Equal(t, GetINT16BitmapMapSize(int16Bitmaps), 3)

	var int32Bitmaps = make(map[int32][]byte)

	int32Bitmaps[int32(1)] = []byte{1}

	assert.Equal(t, GetINT32BitmapMapSize(int32Bitmaps), 5)

}

func TestMotadataStringMap(t *testing.T) {

	value := make(MotadataStringMap)

	assertions := assert.New(t)

	assertions.False(value.IsNotEmpty())

	value["key"] = "value"

	value["key1"] = "value1"

	assertions.True(value.IsNotEmpty())

	assertions.True(value.Contains("key"))

	assertions.False(value.Contains("key3"))

	assertions.True(value.NotContains("key6"))

	assertions.False(value.NotContains("key1"))

}

func TestInterfaceToINT64Values(t *testing.T) {

	assertions := assert.New(t)

	items := make([]interface{}, 1)

	items[0] = int64(math.MaxInt64 - 1)

	values := InterfaceToINT64Values(items)

	assertions.NotNil(values)

	assertions.True(len(values) == 1)

	assertions.Equal(values[0], items[0].(int64))
}

func TestFloatToString(t *testing.T) {

	value := FLOAT64ToStringValue(61.243434)

	fmt.Println(value)

	assert.Equal(t, "61.24", value)

}

func TestUnpack(t *testing.T) {

	table := map[string][]interface{}{}

	dataType := 48

	buffer := bytes.Buffer{}

	buffer.Write(binary.LittleEndian.AppendUint32(make([]byte, 0), 99))

	unpack(table, dataType, &buffer, "demo", 1)

	assert.Equal(t, table["demo"], []interface{}{int32(99)})
}

func TestInvalidUnpack(t *testing.T) {

	assertions := assert.New(t)

	defer func() {

		if r := recover(); r != nil {

			assertions.True(true, "records are not equivalent to the row records")
		}
	}()

	table := map[string][]interface{}{}

	dataType := 48

	buffer := bytes.Buffer{}

	buffer.Write(binary.LittleEndian.AppendUint32(make([]byte, 0), 99))

	unpack(table, dataType, &buffer, "demo", 99)

	assertions.True(false, "this is an equivalent testcases hence records should not be equivalent to row records")
}

func TestGetBytes(t *testing.T) {

	assert.Equal(t, getBytes(48), 4)

	assert.Equal(t, getBytes(0), 0)

}

func TestUpdateConfig(t *testing.T) {

	defer func() {

		if r := recover(); r == nil {

			assert.Fail(t, "error shoud not be nil")

		}
	}()

	UpdateConfigs([]byte("sdflsdjf"), MotadataMap{})

}

func TestCleanUpClickHouseDB(t *testing.T) {

	defer func() {

		if r := recover(); r == nil {

			assert.Fail(t, "error shoud not be nil")

		}
	}()

	conn, _ := InitClickHouse("dummy", "dummy", "dummy")

	CleanUpClickHouseDB(conn, clickhouse.Context(context.Background()), "dummy")
}

func TestCopyDirectory(t *testing.T) {

	assert.NotNil(t, CloneDirectory("", ""))

}

func TestResolveQueryType(t *testing.T) {

	assert.Equal(t, ResolveQueryType("correlated.metric"), int(CorrelationMetric))

	assert.Equal(t, ResolveQueryType("runbook.worklog"), int(RunbookWorklog))

	assert.Equal(t, ResolveQueryType("index"), int(Index))

	assert.Equal(t, ResolveQueryType("policy.result"), int(PolicyResult))

	assert.Equal(t, ResolveQueryType("performance.metric"), int(PerformanceMetric))

	assert.Equal(t, ResolveQueryType("status.metric"), int(ObjectStatusMetric))

	assert.Equal(t, ResolveQueryType("policy.duration.metric"), int(PolicyFlapHistory))

	assert.Equal(t, ResolveQueryType("status.duration.metric"), int(StatusFlapHistory))

	assert.Equal(t, ResolveQueryType("availability"), int(ObjectStatusMetric))

}

func TestCopyBatch(t *testing.T) {

	destinationBatch := MotadataMap{}

	sourceBatch := MotadataMap{}

	sourceBatch["int32"] = map[string][]int32{"demo": {1, 2, 3, 4}}

	sourceBatch["int64"] = map[string][]int64{"demo": {1, 2, 3, 4}}

	sourceBatch["string"] = map[string][]string{"demo": {"demo1"}}

	sourceBatch["none"] = map[string][]byte{"demo": {1, 2, 3, 4}}

	CopyBatch(destinationBatch, sourceBatch)

	assert.NotNil(t, destinationBatch["int32"])

	assert.NotNil(t, destinationBatch["int64"])

	assert.NotNil(t, destinationBatch["string"])

	assert.NotNil(t, destinationBatch["none"])
}

func TestUnixSeconds(t *testing.T) {

	assert.Equal(t, UnixSeconds("", ""), int64(-62135596800))

}

func TestGetEventId(t *testing.T) {

	tick := int32(1)

	assert.Equal(t, GetEventId(tick, 1, 0), int64(100100000))
}

func TestGetDataAggregationSyncTimerSeconds(t *testing.T) {

	configs[DatastoreBrokerDataAggregatorSyncTimerSeconds] = 100

	assert.Equal(t, int64(100), GetDataAggregatorSyncTimerSeconds())

}

func TestGetEventAggregatorsV2(t *testing.T) {

	DeploymentType = Medium

	LocalDatastore = true

	assert.Equal(t, 4, GetEventAggregators())

	configs["datastore.event.aggregators"] = 100

	assert.Equal(t, 100, GetEventAggregators())

}

func TestGetDataAggregators(t *testing.T) {

	DeploymentType = Medium

	LocalDatastore = true

	assert.Equal(t, 4, GetDataAggregators())

	DeploymentType = Large

	LocalDatastore = true

	assert.Equal(t, 6, GetDataAggregators())

	DeploymentType = ExtraLarge

	LocalDatastore = true

	assert.Equal(t, 8, GetDataAggregators())

	DeploymentType = 99

	LocalDatastore = true

	assert.Equal(t, 2, GetDataAggregators())

	configs["datastore.broker.data.aggregators"] = 100

	assert.Equal(t, 100, GetDataAggregators())

}

func TestGetStringValue(t *testing.T) {

	values := MotadataMap{}

	values["int8"] = int8(10)

	assert.Equal(t, values.GetStringValue("int8"), "10")

	values["int16"] = int16(10)

	assert.Equal(t, values.GetStringValue("int16"), "10")

	values["int32"] = int32(10)

	assert.Equal(t, values.GetStringValue("int32"), "10")

}

func TestGetFloatValue(t *testing.T) {

	values := MotadataMap{}

	values["float32"] = float32(11)

	values["float64"] = float64(10)

	values.GetValue("dummy")

	assert.Equal(t, values.GetFloatValue("float32"), float64(11))

	assert.Equal(t, values.GetFloatValue("float64"), float64(10))

}

func TestGetIntValue(t *testing.T) {

	values := MotadataMap{}

	values["int32"] = int32(10)

	values.GetSliceStringValue("dummy")

	values.GetSliceInt32Value("dummy")

	assert.Equal(t, values.GetInt64Value("int32"), int64(10))

}

func TestWeekDay(t *testing.T) {

	assertions := assert.New(t)

	assertions.Equal(getCurrentWeekDay("Monday"), 0)

	assertions.Equal(getCurrentWeekDay("Tuesday"), 1)

	assertions.Equal(getCurrentWeekDay("Wednesday"), 2)

	assertions.Equal(getCurrentWeekDay("Thursday"), 3)

	assertions.Equal(getCurrentWeekDay("Friday"), 4)

	assertions.Equal(getCurrentWeekDay("Saturday"), 5)

	assertions.Equal(getCurrentWeekDay("Sunday"), 6)

	assertions.Equal(getCurrentWeekDay("Sunday1"), 0)
}

func TestReadLogFile(t *testing.T) {

	bytes, err := ReadLogFile("dummy", "dummy")

	assertions := assert.New(t)

	assertions.Nil(bytes)

	assertions.NotNil(err)
}

func TestStatDirEntry(t *testing.T) {

	entry := statDirEntry{}

	assertions := assert.New(t)

	var err error

	entry.info, err = os.Stat(CurrentDir + PathSeparator + ConfigDir + PathSeparator + MotadataDataConfigFile)

	entry.Type()

	entry.Info()

	entry.Name()

	assertions.False(entry.IsDir())

	assertions.NoError(err)

}

func TestUtilsV1(t *testing.T) {

	GetTimeline(LastDay)

	GetTimeline(Custom1Month)

	GetTimeline(Custom1Week)

	EnvironmentType = DatastoreBenchIntegrationEnvironment

	InitTestConfigs()

	assertions := assert.New(t)

	os.Args[0] = "bench"

	assertions.True(SkipBenchmarkTest())

	assertions.Equal(90, DiskIOWorkers)

}

func TestInitConfigs(t *testing.T) {

	assertions := assert.New(t)

	found := InitConfigs([]byte{})

	EnvironmentType = DatastoreDevEnvironment

	defer func() {

		EnvironmentType = DatastoreTestEnvironment
	}()

	assertions.False(found)
}

func TestGetBkpProfile(t *testing.T) {

	assertions := assert.New(t)

	assertions.Equal(GetStoreBackupProfile(PerformanceMetric), "metric")

	assertions.Equal(GetStoreBackupProfile(LogIndex), "log")

	assertions.Equal(GetStoreBackupProfile(Audit), "system.event")

	assertions.Equal(GetStoreBackupProfile(Trap), "trap")

	assertions.Equal(GetStoreBackupProfile(ConfigHistoryIndex), "config.history")

	assertions.Equal(GetStoreBackupProfile(None), "")

	assertions.Equal(GetStoreBackupProfile(Compliance), "compliance")
}

func TestBackupFailingTestEnvironment(t *testing.T) {

	_ = os.MkdirAll(CurrentDir+PathSeparator+DatastoreDir, 0755)

	_ = os.MkdirAll(CurrentDir+PathSeparator+ConfigDir, 0755)

	_ = os.MkdirAll(CurrentDir+PathSeparator+LogDirectory, 0755)

	BackupFailingTestEnvironment(CurrentDir, CurrentDir, "util")

	BackupFailingTestEnvironment(CurrentDir, CurrentDir, "pool")

	_, err := os.Stat("failing-test-envs")

	assertions := assert.New(t)

	assertions.Nil(err)
}

func TestUtilsV2(t *testing.T) {

	assertions := assert.New(t)

	_, found := SearchStringValue(nil, "")

	assertions.False(found)
}

func TestUtilsV3(t *testing.T) {

	assertions := assert.New(t)

	RemapBytes(nil, 0)

	DeploymentType = Medium

	EnvironmentType = "prod"

	configs.Delete("datastore.max.worker.event.key.group.length")

	assertions.Equal(GetMaxWorkerEventKeyGroupLength(), 48)

	assertions.Equal(GetMaxVerticalWriterCacheEntries(), 30000)

}

func TestFastModNV1(t *testing.T) {

	keys := map[string]int{}

	assertions := assert.New(t)

	for i := 1; i <= 100000; i++ {

		keys[INT64ToStringValue(int64(i))] = i

	}

	startTime := time.Date(2025, 1, 1, 0, 0, 0, 0, time.UTC)

	endTime := time.Date(2031, 1, 1, 0, 0, 0, 0, time.UTC)

	plugins := []string{"plugin-1", "plugin-2", "plugin-3", "plugin-4", "plugin-5", "plugin-6"}

	count := 100

	for startTime.Before(endTime) {

		key := INT64ToStringValue(startTime.Unix()) + plugins[rand.Intn(len(plugins))%len(plugins)]

		keys[key] = 0

		startTime = startTime.Add(time.Second)

		count--

		if count%10 == 0 {

			startTime = startTime.AddDate(1, 0, 0)

		}
	}

	for k := range keys {

		index := GetFastModN(city.Hash64([]byte(k)), 10)

		keys[k] = index
	}

	for k := range keys {

		index := GetFastModN(city.Hash64([]byte(k)), 10)

		assertions.Equal(keys[k], index)
	}

}

func TestFastModNV2(t *testing.T) {

	assertions := assert.New(t)

	x := GetFastModN(math.MaxUint64, 1000)

	assertions.LessOrEqual(x, 1000)

	x = GetFastModN(math.MaxUint64, 200)

	assertions.LessOrEqual(x, 200)

}

func TestRedirectStderr(t *testing.T) {

	assertions := assert.New(t)
	// Test 1: Test that RedirectStderr creates the error directory if it doesn't exist

	binPath, err := os.Executable()

	if err != nil {

		assertions.Failf("failed to get executable path: %v", err.Error())
	}

	cmd := exec.Command(binPath, "-test.run=TestHelperProcess")

	cmd.Env = append(os.Environ(), "GO_WANT_HELPER_PROCESS=1")

	err = cmd.Run()

	var exitErr *exec.ExitError

	if errors.As(err, &exitErr) && exitErr.ExitCode() != 0 {

		assertions.Failf("subprocess exited with error: %v", err.Error())
	}

	// Verify error file was created
	_, err = os.Stat(CurrentDir + PathSeparator + ErrorLogFile)

	assertions.NoError(err, "Error file should be created")

	bytes, err := os.ReadFile(CurrentDir + PathSeparator + ErrorLogFile)

	assertions.Nil(err)

	assertions.Contains(string(bytes), "Test stderr redirection")

	err = os.Remove(CurrentDir + PathSeparator + ErrorLogFile)

	assertions.NoError(err)

}

func TestHelperProcess(t *testing.T) {

	if os.Getenv("GO_WANT_HELPER_PROCESS") != "1" {

		return
	}

	err := RedirectStderr()

	if err != nil {

		os.Exit(2)
	}

	_, err = fmt.Fprintf(os.Stderr, "Test stderr redirection")

	if err != nil {

		os.Exit(2)
	}

	os.Exit(0)

}

func TestFuzzyCalculateTickPosition(t *testing.T) {

}

// FuzzCalculateTickPosition tests that CalculateTickPosition and CalculateTickPositionV2
// return the same value for any valid input using Go's fuzzing framework
func FuzzCalculateTickPosition(f *testing.F) {

	// Add seed corpus with various edge cases and representative values
	// Test around epoch time (EpochTimeSeconds = 1577836800 = 2020-01-01 00:00:00 UTC)
	f.Add(int32(0)) // Base case - should represent EpochTimeSeconds
	f.Add(int32(1)) // One second after epoch

	// Test day boundaries (86400 seconds = 1 day)
	f.Add(int32(86400)) // Exactly one day
	f.Add(int32(86399)) // One second before one day
	f.Add(int32(86401)) // One second after one day

	// Test multiple day boundaries
	f.Add(int32(172800)) // Two days (2 * 86400)
	f.Add(int32(259200)) // Three days (3 * 86400)

	// Test various times within a day
	f.Add(int32(3600))  // 1 hour (3600 seconds)
	f.Add(int32(7200))  // 2 hours
	f.Add(int32(43200)) // 12 hours (noon)
	f.Add(int32(82800)) // 23 hours

	// Test edge cases near int32 limits
	f.Add(int32(2147483647)) // math.MaxInt32
	f.Add(int32(2147483646)) // MaxInt32 - 1

	// Test some random representative values
	f.Add(int32(1234567)) // Random positive value
	f.Add(int32(999999))  // Another random value

	f.Fuzz(func(t *testing.T, tick int32) {

		// Call both functions with the same input
		result1 := CalculateTickPosition(tick)
		result2 := CalculateTickPositionV2(tick)

		// Both functions should return the same value
		if result1 != result2 {
			t.Errorf("CalculateTickPosition(%d) = %d, but CalculateTickPositionV2(%d) = %d; expected both to return the same value",
				tick, result1, tick, result2)
		}

		// Additional validation: both results should be in valid range [0, 86399]
		// since they represent seconds within a day
		if result1 < 0 || result1 >= 86400 {
			t.Errorf("CalculateTickPosition(%d) returned %d, which is outside valid range [0, 86399]", tick, result1)
		}

		if result2 < 0 || result2 >= 86400 {
			t.Errorf("CalculateTickPositionV2(%d) returned %d, which is outside valid range [0, 86399]", tick, result2)
		}

		// Verify the mathematical relationship: both should represent seconds since midnight
		// For debugging purposes, we can also verify the calculation manually
		expectedV2 := int(tick % 86400)
		if expectedV2 < 0 {
			expectedV2 += 86400 // Handle negative modulo in Go
		}

		if result2 != expectedV2 {
			t.Errorf("CalculateTickPositionV2(%d) = %d, but manual calculation gives %d", tick, result2, expectedV2)
		}
	})
}

// ----- Test Cases -----

type testCase struct {
	name                   string
	from                   string // in "2006-01-02" format
	to                     string
	expectedWeekStartDates []string // each as "2006-01-02"
}

func parseDate(s string) time.Time {
	// Parse date assuming location is local and time set to noon for simplicity.
	t, _ := time.Parse("2006-01-02", s)
	return t
}

func TestQualifyWeeklyDates(t *testing.T) {

	testCases := []testCase{
		{
			name:                   "SameDay_WeekStart",
			from:                   "2023-08-07", // Monday
			to:                     "2023-08-07", // same Monday
			expectedWeekStartDates: []string{"2023-08-07"},
		},
		{
			name:                   "SameDay_NonWeekStart",
			from:                   "2023-08-09", // Wednesday; week starts on Monday (2023-08-07)
			to:                     "2023-08-09",
			expectedWeekStartDates: []string{"2023-08-07"},
		},
		{
			name:                   "WithinSameWeek",
			from:                   "2023-08-09", // Wednesday -> week start: 2023-08-07
			to:                     "2023-08-15", // Tuesday -> week start: 2023-08-14
			expectedWeekStartDates: []string{"2023-08-07", "2023-08-14"},
		},
		{
			name:                   "AcrossWeekBoundaries",
			from:                   "2023-08-06", // Sunday -> week start: 2023-07-31
			to:                     "2023-08-13", // Sunday -> week start: 2023-08-07
			expectedWeekStartDates: []string{"2023-07-31", "2023-08-07"},
		},
		{
			name:                   "MultipleWeeks",
			from:                   "2023-08-01", // Tuesday -> week start: 2023-07-31
			to:                     "2023-08-31", // Thursday -> week start: 2023-08-28
			expectedWeekStartDates: []string{"2023-07-31", "2023-08-07", "2023-08-14", "2023-08-21", "2023-08-28"},
		},
		{
			name: "FromAfterTo",
			from: "2023-08-15", // Tuesday -> week start: 2023-08-14
			to:   "2023-08-09", // Wednesday (earlier week) -> week start: 2023-08-07
			// Since the loop will break immediately, we expect only the starting week of fromDateTime.
			expectedWeekStartDates: []string{"2023-08-14"},
		},
		{
			name:                   "ExactBoundary",
			from:                   "2023-08-07", // Monday
			to:                     "2023-08-14", // next Monday
			expectedWeekStartDates: []string{"2023-08-07", "2023-08-14"},
		},
		{
			name:                   "CrossingYearBoundary",
			from:                   "2022-12-30", // Friday -> week start: 2022-12-26
			to:                     "2023-01-05", // Thursday -> week start: 2023-01-02
			expectedWeekStartDates: []string{"2022-12-26", "2023-01-02"},
		},
		{
			name: "LongPeriod",
			from: "2023-01-01", // Sunday -> week start: 2022-12-26
			to:   "2023-03-01", // Wednesday -> week start: 2023-02-27
			expectedWeekStartDates: []string{
				"2022-12-26",
				"2023-01-02",
				"2023-01-09",
				"2023-01-16",
				"2023-01-23",
				"2023-01-30",
				"2023-02-06",
				"2023-02-13",
				"2023-02-20",
				"2023-02-27",
			},
		},
	}

	for _, testCase := range testCases {

		t.Run(testCase.name, func(t *testing.T) {

			from := parseDate(testCase.from)

			to := parseDate(testCase.to)

			result := QualifyWeeklyDates(from, to)

			// Convert result slice to a slice of strings in "2006-01-02" format.
			var resultDates []string

			for _, d := range result {

				resultDates = append(resultDates, d.Format("2006-01-02"))
			}

			// Compare lengths first.
			if len(resultDates) != len(testCase.expectedWeekStartDates) {

				t.Fatalf("expected %d week start dates, got %d: %v", len(testCase.expectedWeekStartDates), len(resultDates), resultDates)
			}

			// Compare each expected date.
			for i, expected := range testCase.expectedWeekStartDates {

				if resultDates[i] != expected {

					t.Errorf("at index %d, expected %s but got %s", i, expected, resultDates[i])
				}
			}
		})
	}
}
